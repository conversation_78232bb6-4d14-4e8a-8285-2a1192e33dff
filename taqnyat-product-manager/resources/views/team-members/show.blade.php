@extends('layouts.app')

@section('page-title', $teamMember->name)

@section('content')
<div class="max-w-6xl mx-auto">
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="{{ route('team-members.index') }}" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="flex items-center space-x-4">
                    <div class="w-16 h-16 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-2xl font-bold">
                        {{ substr($teamMember->name, 0, 1) }}
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ $teamMember->name }}</h1>
                        <div class="flex items-center space-x-4 mt-1">
                            <span class="text-gray-600">{{ $teamMember->email }}</span>
                            @if($teamMember->is_active)
                                <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                    Active
                                </span>
                            @else
                                <span class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                                    Inactive
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex space-x-2">
                <a href="{{ route('team-members.edit', $teamMember) }}" 
                   class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-edit mr-2"></i>
                    Edit
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Role & Responsibilities -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Role & Responsibilities</h2>
                <div class="space-y-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-700">Current Role</h3>
                        <p class="text-gray-900">{{ $teamMember->role ?? 'No role assigned' }}</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-700">Responsibilities</h3>
                        @if($teamMember->responsibilities)
                            <p class="text-gray-700 leading-relaxed">{{ $teamMember->responsibilities }}</p>
                        @else
                            <p class="text-gray-500 italic">No responsibilities defined</p>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Assigned Products -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Assigned Products</h2>
                @if($teamMember->products->count() > 0)
                    <div class="space-y-4">
                        @foreach($teamMember->products as $product)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3">
                                            <h3 class="font-medium text-gray-900">{{ $product->name }}</h3>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full {{ $product->phase_badge_color }}">
                                                {{ ucfirst(str_replace('_', ' ', $product->phase)) }}
                                            </span>
                                        </div>
                                        @if($product->pivot->role_in_product)
                                            <p class="text-sm text-primary-600 mt-1">{{ $product->pivot->role_in_product }}</p>
                                        @endif
                                        @if($product->description)
                                            <p class="text-sm text-gray-600 mt-2">{{ Str::limit($product->description, 100) }}</p>
                                        @endif
                                    </div>
                                    <a href="{{ route('products.show', $product) }}" 
                                       class="text-primary-600 hover:text-primary-700 ml-4">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-box text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No products assigned</p>
                    </div>
                @endif
            </div>

            <!-- KPIs Performance -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">KPIs & Performance</h2>
                @if($teamMember->kpis->count() > 0)
                    <div class="space-y-4">
                        @foreach($teamMember->kpis as $kpi)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="font-medium text-gray-900">{{ $kpi->name }}</h3>
                                    @if($kpi->is_active)
                                        <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                            Active
                                        </span>
                                    @else
                                        <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                            Inactive
                                        </span>
                                    @endif
                                </div>
                                <div class="grid grid-cols-3 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-600">Target:</span>
                                        <span class="font-medium">{{ $kpi->target_value }} {{ $kpi->metric_type }}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-600">Period:</span>
                                        <span class="font-medium">{{ ucfirst($kpi->period) }}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-600">Evaluations:</span>
                                        <span class="font-medium">{{ $kpi->evaluations->count() }}</span>
                                    </div>
                                </div>
                                @if($kpi->description)
                                    <p class="text-sm text-gray-600 mt-2">{{ $kpi->description }}</p>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-chart-bar text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No KPIs defined</p>
                        <a href="{{ route('kpis.create') }}" class="text-primary-600 hover:text-primary-700 text-sm font-medium mt-2 inline-block">
                            Create KPI for this member
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h2>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Products</span>
                        <span class="font-medium">{{ $teamMember->products->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total KPIs</span>
                        <span class="font-medium">{{ $teamMember->kpis->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Active KPIs</span>
                        <span class="font-medium">{{ $teamMember->activeKpis->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Member Since</span>
                        <span class="font-medium">{{ $teamMember->created_at->format('M Y') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Last Updated</span>
                        <span class="font-medium">{{ $teamMember->updated_at->format('M d, Y') }}</span>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h2>
                <div class="space-y-3">
                    <div>
                        <span class="text-sm text-gray-600">Email</span>
                        <p class="font-medium">{{ $teamMember->email }}</p>
                    </div>
                    <div>
                        <span class="text-sm text-gray-600">Status</span>
                        <p class="font-medium">
                            @if($teamMember->is_active)
                                <span class="text-green-600">Active</span>
                            @else
                                <span class="text-red-600">Inactive</span>
                            @endif
                        </p>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Actions</h2>
                <div class="space-y-2">
                    <a href="{{ route('team-members.edit', $teamMember) }}" 
                       class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Member
                    </a>
                    <a href="{{ route('kpis.create') }}?team_member={{ $teamMember->id }}" 
                       class="w-full flex items-center justify-center px-4 py-2 border border-primary-300 text-primary-700 rounded-md hover:bg-primary-50 transition-colors">
                        <i class="fas fa-chart-line mr-2"></i>
                        Add KPI
                    </a>
                    <button onclick="deleteTeamMember()" 
                            class="w-full flex items-center justify-center px-4 py-2 border border-red-300 text-red-700 rounded-md hover:bg-red-50 transition-colors">
                        <i class="fas fa-trash mr-2"></i>
                        Delete Member
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function deleteTeamMember() {
    if (!confirm('Are you sure you want to delete this team member? This action cannot be undone.')) {
        return;
    }

    $.ajax({
        url: '{{ route("team-members.destroy", $teamMember) }}',
        type: 'DELETE',
        success: function(response) {
            if (response.success) {
                window.location.href = '{{ route("team-members.index") }}';
            }
        },
        error: function() {
            alert('Error deleting team member');
        }
    });
}
</script>
@endpush
@endsection
