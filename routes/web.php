<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\TeamMemberController;
use App\Http\Controllers\KpiController;
use App\Http\Controllers\EvaluationController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\WebhookController;
use App\Http\Controllers\ProductDocumentController;

// Redirect root to dashboard
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

// Products routes
Route::resource('products', ProductController::class);
Route::post('products/{product}/sync-clickup', [ProductController::class, 'syncWithClickUp'])->name('products.sync-clickup');
Route::post('products/{product}/refresh-clickup', [ProductController::class, 'refreshClickUpData'])->name('products.refresh-clickup');
Route::post('products/{product}/clickup-lists', [ProductController::class, 'addClickupList'])->name('products.add-clickup-list');
Route::delete('products/{product}/clickup-lists/{assignmentId}', [ProductController::class, 'removeClickupList'])->name('products.remove-clickup-list');

// Product Documents routes
Route::prefix('products/{product}/documents')->name('products.documents.')->group(function () {
    Route::get('/', [ProductDocumentController::class, 'index'])->name('index');
    Route::get('/create', [ProductDocumentController::class, 'create'])->name('create');
    Route::get('/upload-limits', [ProductDocumentController::class, 'getUploadLimits'])->name('upload-limits');
    Route::post('/', [ProductDocumentController::class, 'store'])->name('store');
    Route::get('/{document}', [ProductDocumentController::class, 'show'])->name('show');
    Route::get('/{document}/download', [ProductDocumentController::class, 'download'])->name('download');
    Route::get('/{document}/edit', [ProductDocumentController::class, 'edit'])->name('edit');
    Route::put('/{document}', [ProductDocumentController::class, 'update'])->name('update');
    Route::delete('/{document}', [ProductDocumentController::class, 'destroy'])->name('destroy');
    Route::get('/{document}/versions', [ProductDocumentController::class, 'versions'])->name('versions');
});

// Team Members routes
Route::resource('team-members', TeamMemberController::class);
Route::patch('team-members/{teamMember}/toggle-status', [TeamMemberController::class, 'toggleStatus'])->name('team-members.toggle-status');

// KPIs routes
Route::resource('kpis', KpiController::class);

// Evaluations routes
Route::resource('evaluations', EvaluationController::class);

// Reports routes
Route::get('reports', [ReportsController::class, 'index'])->name('reports.index');
Route::get('reports/team-performance', [ReportsController::class, 'teamPerformance'])->name('reports.team-performance');
Route::get('reports/product-progress', [ReportsController::class, 'productProgress'])->name('reports.product-progress');

// Settings routes
Route::get('settings', [SettingsController::class, 'index'])->name('settings.index');
Route::put('settings', [SettingsController::class, 'update'])->name('settings.update');
Route::post('settings/test-clickup', [SettingsController::class, 'testClickUpConnection'])->name('settings.test-clickup');
Route::get('settings/clickup-teams', [SettingsController::class, 'getClickUpTeams'])->name('settings.clickup-teams');
Route::get('settings/clickup-spaces', [SettingsController::class, 'getClickUpSpaces'])->name('settings.clickup-spaces');
Route::get('settings/clickup-folders', [SettingsController::class, 'getClickUpFolders'])->name('settings.clickup-folders');
Route::get('settings/clickup-lists', [SettingsController::class, 'getClickUpLists'])->name('settings.clickup-lists');

// Webhook routes (exclude from CSRF protection)
Route::post('webhooks/clickup', [WebhookController::class, 'clickup'])->name('webhooks.clickup');
