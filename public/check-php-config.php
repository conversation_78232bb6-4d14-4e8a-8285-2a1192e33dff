<?php
// Simple script to check PHP upload configuration
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>PHP Upload Configuration Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .config-table { border-collapse: collapse; width: 100%; }
        .config-table th, .config-table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        .config-table th { background-color: #f2f2f2; }
        .status { padding: 8px 16px; border-radius: 4px; font-weight: bold; }
        .status.good { background-color: #d4edda; color: #155724; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>PHP Upload Configuration Status</h1>
    
    <?php
    $uploadMax = ini_get('upload_max_filesize');
    $postMax = ini_get('post_max_size');
    $memoryLimit = ini_get('memory_limit');
    $maxExecTime = ini_get('max_execution_time');
    
    function parseSize($size) {
        $size = trim($size);
        $last = strtolower($size[strlen($size) - 1]);
        $size = (int) $size;
        switch ($last) {
            case 'g': $size *= 1024;
            case 'm': $size *= 1024;
            case 'k': $size *= 1024;
        }
        return $size;
    }
    
    $uploadBytes = parseSize($uploadMax);
    $postBytes = parseSize($postMax);
    $uploadMB = round($uploadBytes / 1024 / 1024, 1);
    $postMB = round($postBytes / 1024 / 1024, 1);
    
    $status = 'error';
    $message = 'Upload limits too low (< 10MB)';
    
    if ($uploadMB >= 10 && $postMB >= 10) {
        $status = 'good';
        $message = 'Upload limits configured correctly (≥ 10MB)';
    } elseif ($uploadMB >= 5 && $postMB >= 5) {
        $status = 'warning';
        $message = 'Upload limits moderate (5-10MB)';
    }
    ?>
    
    <div class="status <?php echo $status; ?>">
        Status: <?php echo $message; ?>
    </div>
    
    <h2>Current Configuration</h2>
    <table class="config-table">
        <tr>
            <th>Setting</th>
            <th>Value</th>
            <th>Bytes</th>
            <th>MB</th>
        </tr>
        <tr>
            <td>upload_max_filesize</td>
            <td><?php echo $uploadMax; ?></td>
            <td><?php echo number_format($uploadBytes); ?></td>
            <td><?php echo $uploadMB; ?></td>
        </tr>
        <tr>
            <td>post_max_size</td>
            <td><?php echo $postMax; ?></td>
            <td><?php echo number_format($postBytes); ?></td>
            <td><?php echo $postMB; ?></td>
        </tr>
        <tr>
            <td>memory_limit</td>
            <td><?php echo $memoryLimit; ?></td>
            <td><?php echo number_format(parseSize($memoryLimit)); ?></td>
            <td><?php echo round(parseSize($memoryLimit) / 1024 / 1024, 1); ?></td>
        </tr>
        <tr>
            <td>max_execution_time</td>
            <td><?php echo $maxExecTime; ?> seconds</td>
            <td>-</td>
            <td>-</td>
        </tr>
    </table>
    
    <h2>Effective Upload Limit</h2>
    <p>The effective upload limit is the smaller of upload_max_filesize and post_max_size:</p>
    <div class="status <?php echo $status; ?>">
        Effective Limit: <?php echo min($uploadMB, $postMB); ?>MB
    </div>
    
    <h2>Instructions</h2>
    <ol>
        <li><strong>If limits are still 2MB:</strong> Restart Herd application completely (Quit and reopen)</li>
        <li><strong>After restarting:</strong> Refresh this page to verify the new limits</li>
        <li><strong>Expected result:</strong> Both upload_max_filesize and post_max_size should show 12M</li>
    </ol>
    
    <p><a href="<?php echo $_SERVER['REQUEST_URI']; ?>">Refresh this page</a> | 
       <a href="/products">Back to Products</a></p>
    
    <hr>
    <p><small>Last checked: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
