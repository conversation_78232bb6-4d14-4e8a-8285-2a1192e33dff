<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Storage;

class Product extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'description',
        'icon',
        'phase',
        'start_date',
        'target_date',
        'completion_date',
        'clickup_task_id',
        'clickup_data',
        'clickup_list_id',
        'clickup_list_data',
    ];

    protected $casts = [
        'start_date' => 'date',
        'target_date' => 'date',
        'completion_date' => 'date',
        'clickup_data' => 'array',
        'clickup_list_data' => 'array',
    ];

    /**
     * Get the team members assigned to this product
     */
    public function teamMembers(): BelongsToMany
    {
        return $this->belongsToMany(TeamMember::class)
            ->withPivot('role_in_product')
            ->withTimestamps();
    }

    /**
     * Get the documents for this product
     */
    public function documents(): HasMany
    {
        return $this->hasMany(ProductDocument::class);
    }

    /**
     * Get the ClickUp lists assigned to this product
     */
    public function clickupLists(): HasMany
    {
        return $this->hasMany(ProductClickupList::class);
    }

    /**
     * Get the current version documents for this product
     */
    public function currentDocuments(): HasMany
    {
        return $this->hasMany(ProductDocument::class)->where('is_current_version', true);
    }

    /**
     * Get the icon URL for this product
     */
    public function getIconUrlAttribute(): string
    {
        if ($this->icon && Storage::disk('public')->exists($this->icon)) {
            return Storage::disk('public')->url($this->icon);
        }

        return $this->getDefaultIconUrl();
    }

    /**
     * Get the default icon URL based on product phase
     */
    public function getDefaultIconUrl(): string
    {
        $defaultIcons = [
            'idea' => '/images/default-icons/idea.svg',
            'in_progress' => '/images/default-icons/in-progress.svg',
            'done' => '/images/default-icons/done.svg',
        ];

        return $defaultIcons[$this->phase] ?? '/images/default-icons/default.svg';
    }

    /**
     * Check if product has a custom icon
     */
    public function hasCustomIcon(): bool
    {
        return !empty($this->icon) && Storage::disk('public')->exists($this->icon);
    }

    /**
     * Delete the product icon file
     */
    public function deleteIcon(): void
    {
        if ($this->icon && Storage::disk('public')->exists($this->icon)) {
            Storage::disk('public')->delete($this->icon);
        }
    }

    /**
     * Get the phase badge color
     */
    public function phaseBadgeColor(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->phase) {
                'idea' => 'bg-gray-100 text-gray-800',
                'in_progress' => 'bg-blue-100 text-blue-800',
                'done' => 'bg-green-100 text-green-800',
                default => 'bg-gray-100 text-gray-800',
            }
        );
    }

    /**
     * Check if product is overdue
     */
    public function isOverdue(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->target_date &&
                         $this->phase !== 'done' &&
                         $this->target_date < now()->toDateString()
        );
    }
}
