<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class ProductDocument extends Model
{
    protected $fillable = [
        'product_id',
        'title',
        'description',
        'file_name',
        'file_path',
        'file_type',
        'file_size',
        'version',
        'version_major',
        'version_minor',
        'version_patch',
        'uploaded_by',
        'is_current_version',
        'uploaded_at',
    ];

    protected $casts = [
        'uploaded_at' => 'datetime',
        'is_current_version' => 'boolean',
        'file_size' => 'integer',
        'version_major' => 'integer',
        'version_minor' => 'integer',
        'version_patch' => 'integer',
    ];

    /**
     * Get the product that owns the document
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the team member who uploaded the document
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(TeamMember::class, 'uploaded_by');
    }

    /**
     * Get all versions of this document (same title)
     */
    public function versions()
    {
        return $this->hasMany(ProductDocument::class, 'product_id', 'product_id')
            ->where('title', $this->title)
            ->orderBy('version_major', 'desc')
            ->orderBy('version_minor', 'desc')
            ->orderBy('version_patch', 'desc');
    }

    /**
     * Get the file size in human readable format
     */
    public function getFileSizeHumanAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the download URL for the document
     */
    public function getDownloadUrlAttribute(): string
    {
        return route('products.documents.download', ['product' => $this->product_id, 'document' => $this->id]);
    }

    /**
     * Check if this is the latest version of the document
     */
    public function isLatestVersion(): bool
    {
        return $this->is_current_version;
    }

    /**
     * Get the next version number for a document title
     */
    public static function getNextVersion(int $productId, string $title): array
    {
        $latestDoc = static::where('product_id', $productId)
            ->where('title', $title)
            ->orderBy('version_major', 'desc')
            ->orderBy('version_minor', 'desc')
            ->orderBy('version_patch', 'desc')
            ->first();

        if (!$latestDoc) {
            return [
                'version' => '1.0.0',
                'major' => 1,
                'minor' => 0,
                'patch' => 0
            ];
        }

        // Increment minor version by default
        $newMinor = $latestDoc->version_minor + 1;

        return [
            'version' => "{$latestDoc->version_major}.{$newMinor}.0",
            'major' => $latestDoc->version_major,
            'minor' => $newMinor,
            'patch' => 0
        ];
    }

    /**
     * Mark this document as the current version and others as old
     */
    public function markAsCurrent(): void
    {
        // Mark all other versions of this document as not current
        static::where('product_id', $this->product_id)
            ->where('title', $this->title)
            ->where('id', '!=', $this->id)
            ->update(['is_current_version' => false]);

        // Mark this document as current
        $this->update(['is_current_version' => true]);
    }

    /**
     * Delete the physical file when the model is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($document) {
            if (Storage::disk('public')->exists($document->file_path)) {
                Storage::disk('public')->delete($document->file_path);
            }
        });
    }
}
