<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TeamMemberClickupList extends Model
{
    use HasFactory;

    protected $fillable = [
        'team_member_id',
        'clickup_list_id',
        'list_type',
        'clickup_list_data',
        'clickup_space_data',
        'clickup_folder_data',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'clickup_list_data' => 'array',
        'clickup_space_data' => 'array',
        'clickup_folder_data' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the team member that owns this ClickUp list assignment
     */
    public function teamMember(): BelongsTo
    {
        return $this->belongsTo(TeamMember::class);
    }

    /**
     * Get the list type options
     */
    public static function getListTypes(): array
    {
        return [
            'features' => 'Features',
            'bugs' => 'Bugs',
            'other' => 'Other',
        ];
    }

    /**
     * Get the formatted list type name
     */
    public function getListTypeNameAttribute(): string
    {
        return self::getListTypes()[$this->list_type] ?? 'Other';
    }

    /**
     * Get the hierarchical path for this ClickUp list
     */
    public function getHierarchicalPathAttribute(): string
    {
        $space = $this->clickup_space_data['name'] ?? 'Unknown Space';
        $folder = $this->clickup_folder_data['name'] ?? null;
        $list = $this->clickup_list_data['name'] ?? 'Unknown List';

        if ($folder) {
            return "{$space} > {$folder} > {$list}";
        }

        return "{$space} > No Folder > {$list}";
    }

    /**
     * Get the list type badge color
     */
    public function getListTypeBadgeColorAttribute(): string
    {
        return match($this->list_type) {
            'features' => 'bg-blue-100 text-blue-800',
            'bugs' => 'bg-red-100 text-red-800',
            'other' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Scope to get active assignments
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by list type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('list_type', $type);
    }
}
