<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TeamMember extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'email',
        'role',
        'responsibilities',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the products assigned to this team member
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class)
            ->withPivot('role_in_product')
            ->withTimestamps();
    }

    /**
     * Get the KPIs for this team member
     */
    public function kpis(): HasMany
    {
        return $this->hasMany(Kpi::class);
    }

    /**
     * Get active KPIs for this team member
     */
    public function activeKpis(): HasMany
    {
        return $this->hasMany(Kpi::class)->where('is_active', true);
    }

    /**
     * Get the ClickUp lists assigned to this team member
     */
    public function clickupLists(): Has<PERSON>any
    {
        return $this->hasMany(TeamMemberClickupList::class);
    }

    /**
     * Get active ClickUp list assignments for this team member
     */
    public function activeClickupLists(): HasMany
    {
        return $this->hasMany(TeamMemberClickupList::class)->where('is_active', true);
    }

    /**
     * Get ClickUp lists by type
     */
    public function clickupListsByType(string $type): HasMany
    {
        return $this->hasMany(TeamMemberClickupList::class)->where('list_type', $type)->where('is_active', true);
    }

    /**
     * Check if team member has any ClickUp list assignments
     */
    public function hasClickupLists(): bool
    {
        return $this->clickupLists()->where('is_active', true)->exists();
    }

    /**
     * Get ClickUp assignment statistics
     */
    public function getClickupStatsAttribute(): array
    {
        $lists = $this->activeClickupLists;

        return [
            'total' => $lists->count(),
            'features' => $lists->where('list_type', 'features')->count(),
            'bugs' => $lists->where('list_type', 'bugs')->count(),
            'other' => $lists->where('list_type', 'other')->count(),
        ];
    }
}
