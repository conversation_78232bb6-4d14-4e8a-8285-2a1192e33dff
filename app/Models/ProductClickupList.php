<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductClickupList extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'clickup_list_id',
        'clickup_list_data',
        'list_type',
    ];

    protected $casts = [
        'clickup_list_data' => 'array',
    ];

    /**
     * Get the product that owns this ClickUp list assignment
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the list type options
     */
    public static function getListTypes(): array
    {
        return [
            'features' => 'Features',
            'bugs' => 'Bugs',
            'other' => 'Other',
        ];
    }

    /**
     * Get the formatted list type name
     */
    public function getListTypeNameAttribute(): string
    {
        return self::getListTypes()[$this->list_type] ?? 'Other';
    }
}
