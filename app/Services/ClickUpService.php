<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class ClickUpService
{
    private $apiToken;
    private $teamId;
    private $baseUrl = 'https://api.clickup.com/api/v2';
    private $rateLimitDelay = 1; // seconds between requests

    public function __construct()
    {
        $this->apiToken = Setting::get('clickup_api_token');
        $this->teamId = Setting::get('clickup_team_id');
    }

    /**
     * Set API token temporarily for testing
     */
    public function setApiToken(string $token): void
    {
        $this->apiToken = $token;
    }

    /**
     * Set team ID temporarily for testing
     */
    public function setTeamId(string $teamId): void
    {
        $this->teamId = $teamId;
    }

    /**
     * Check if ClickUp integration is enabled and configured
     */
    public function isEnabled(): bool
    {
        return Setting::get('clickup_sync_enabled', false) && 
               !empty($this->apiToken) && 
               !empty($this->teamId);
    }

    /**
     * Test ClickUp API connection
     */
    public function testConnection(): array
    {
        if (empty($this->apiToken)) {
            return ['success' => false, 'message' => 'API token not configured'];
        }

        try {
            Log::info('Testing ClickUp connection', ['token_length' => strlen($this->apiToken)]);
            $response = $this->makeRequest('GET', '/user');

            if ($response['success']) {
                return [
                    'success' => true,
                    'message' => 'Connection successful',
                    'user' => $response['data']['user'] ?? null
                ];
            }

            Log::warning('ClickUp connection test failed', $response);
            return ['success' => false, 'message' => $response['message']];
        } catch (\Exception $e) {
            Log::error('ClickUp connection test exception', ['error' => $e->getMessage()]);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Get ClickUp teams
     */
    public function getTeams(): array
    {
        try {
            $response = $this->makeRequest('GET', '/team');
            
            if ($response['success']) {
                return [
                    'success' => true,
                    'teams' => $response['data']['teams'] ?? []
                ];
            }
            
            return ['success' => false, 'message' => $response['message']];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Get spaces for the configured team
     */
    public function getSpaces(): array
    {
        if (empty($this->teamId)) {
            return ['success' => false, 'message' => 'Team ID not configured'];
        }

        try {
            $response = $this->makeRequest('GET', "/team/{$this->teamId}/space");

            if ($response['success']) {
                return [
                    'success' => true,
                    'spaces' => $response['data']['spaces'] ?? []
                ];
            }

            return ['success' => false, 'message' => $response['message']];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Get folders for a specific space
     */
    public function getFolders(string $spaceId): array
    {
        try {
            $response = $this->makeRequest('GET', "/space/{$spaceId}/folder");

            if ($response['success']) {
                return [
                    'success' => true,
                    'folders' => $response['data']['folders'] ?? []
                ];
            }

            return ['success' => false, 'message' => $response['message']];
        } catch (\Exception $e) {
            \Log::error('ClickUp API error (getFolders)', [
                'space_id' => $spaceId,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Get lists for a specific folder
     */
    public function getListsFromFolder(string $folderId): array
    {
        try {
            $response = $this->makeRequest('GET', "/folder/{$folderId}/list");

            if ($response['success']) {
                return [
                    'success' => true,
                    'lists' => $response['data']['lists'] ?? []
                ];
            }

            return ['success' => false, 'message' => $response['message']];
        } catch (\Exception $e) {
            \Log::error('ClickUp API error (getListsFromFolder)', [
                'folder_id' => $folderId,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Get lists for a specific space (includes folderless lists)
     */
    public function getLists(string $spaceId): array
    {
        try {
            $response = $this->makeRequest('GET', "/space/{$spaceId}/list");

            if ($response['success']) {
                return [
                    'success' => true,
                    'lists' => $response['data']['lists'] ?? []
                ];
            }

            return ['success' => false, 'message' => $response['message']];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Get list details
     */
    public function getList(string $listId): array
    {
        try {
            $response = $this->makeRequest('GET', "/list/{$listId}");

            if ($response['success']) {
                return [
                    'success' => true,
                    'list' => $response['data']
                ];
            }

            return ['success' => false, 'message' => $response['message']];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Get tasks in a specific list
     */
    public function getListTasks(string $listId): array
    {
        try {
            $response = $this->makeRequest('GET', "/list/{$listId}/task");

            if ($response['success']) {
                return [
                    'success' => true,
                    'tasks' => $response['data']['tasks'] ?? []
                ];
            }

            return ['success' => false, 'message' => $response['message']];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Create a new task in ClickUp
     */
    public function createTask(array $taskData, string $listId = null): array
    {
        if (!$this->isEnabled()) {
            return ['success' => false, 'message' => 'ClickUp integration not enabled'];
        }

        // Use provided list ID or fall back to default space
        if (empty($listId)) {
            $spaceId = Setting::get('clickup_space_id');
            if (empty($spaceId)) {
                return ['success' => false, 'message' => 'ClickUp space not configured'];
            }
            $endpoint = "/list/{$spaceId}/task";
        } else {
            $endpoint = "/list/{$listId}/task";
        }

        try {
            $payload = [
                'name' => $taskData['name'],
                'description' => $taskData['description'] ?? '',
                'status' => $this->mapPhaseToStatus($taskData['phase'] ?? 'idea'),
                'priority' => $taskData['priority'] ?? 3,
                'due_date' => isset($taskData['due_date']) ? strtotime($taskData['due_date']) * 1000 : null,
                'start_date' => isset($taskData['start_date']) ? strtotime($taskData['start_date']) * 1000 : null,
            ];

            // Remove null values
            $payload = array_filter($payload, function($value) {
                return $value !== null;
            });

            $response = $this->makeRequest('POST', $endpoint, $payload);
            
            if ($response['success']) {
                Log::info('ClickUp task created', ['task_id' => $response['data']['id']]);
                return [
                    'success' => true,
                    'task' => $response['data']
                ];
            }
            
            return ['success' => false, 'message' => $response['message']];
        } catch (\Exception $e) {
            Log::error('ClickUp task creation failed', ['error' => $e->getMessage()]);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Update an existing task in ClickUp
     */
    public function updateTask(string $taskId, array $updates): array
    {
        if (!$this->isEnabled()) {
            return ['success' => false, 'message' => 'ClickUp integration not enabled'];
        }

        try {
            $payload = [];
            
            if (isset($updates['name'])) {
                $payload['name'] = $updates['name'];
            }
            
            if (isset($updates['description'])) {
                $payload['description'] = $updates['description'];
            }
            
            if (isset($updates['phase'])) {
                $payload['status'] = $this->mapPhaseToStatus($updates['phase']);
            }
            
            if (isset($updates['due_date'])) {
                $payload['due_date'] = strtotime($updates['due_date']) * 1000;
            }

            if (empty($payload)) {
                return ['success' => true, 'message' => 'No updates to sync'];
            }

            $response = $this->makeRequest('PUT', "/task/{$taskId}", $payload);
            
            if ($response['success']) {
                Log::info('ClickUp task updated', ['task_id' => $taskId]);
                return [
                    'success' => true,
                    'task' => $response['data']
                ];
            }
            
            return ['success' => false, 'message' => $response['message']];
        } catch (\Exception $e) {
            Log::error('ClickUp task update failed', ['task_id' => $taskId, 'error' => $e->getMessage()]);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Get task details from ClickUp
     */
    public function getTask(string $taskId): array
    {
        if (!$this->isEnabled()) {
            return ['success' => false, 'message' => 'ClickUp integration not enabled'];
        }

        try {
            $response = $this->makeRequest('GET', "/task/{$taskId}");
            
            if ($response['success']) {
                return [
                    'success' => true,
                    'task' => $response['data']
                ];
            }
            
            return ['success' => false, 'message' => $response['message']];
        } catch (\Exception $e) {
            Log::error('ClickUp task fetch failed', ['task_id' => $taskId, 'error' => $e->getMessage()]);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Delete a task from ClickUp
     */
    public function deleteTask(string $taskId): array
    {
        if (!$this->isEnabled()) {
            return ['success' => false, 'message' => 'ClickUp integration not enabled'];
        }

        try {
            $response = $this->makeRequest('DELETE', "/task/{$taskId}");
            
            if ($response['success']) {
                Log::info('ClickUp task deleted', ['task_id' => $taskId]);
                return ['success' => true];
            }
            
            return ['success' => false, 'message' => $response['message']];
        } catch (\Exception $e) {
            Log::error('ClickUp task deletion failed', ['task_id' => $taskId, 'error' => $e->getMessage()]);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Map product phase to ClickUp status
     */
    private function mapPhaseToStatus(string $phase): string
    {
        return match($phase) {
            'idea' => 'to do',
            'in_progress' => 'in progress',
            'done' => 'complete',
            default => 'to do'
        };
    }

    /**
     * Map ClickUp status to product phase
     */
    public function mapStatusToPhase(string $status): string
    {
        $status = strtolower($status);
        
        if (in_array($status, ['complete', 'completed', 'done', 'closed'])) {
            return 'done';
        }
        
        if (in_array($status, ['in progress', 'in review', 'testing', 'active'])) {
            return 'in_progress';
        }
        
        return 'idea';
    }

    /**
     * Make HTTP request to ClickUp API with rate limiting
     */
    private function makeRequest(string $method, string $endpoint, array $data = []): array
    {
        // Rate limiting
        $this->enforceRateLimit();

        try {
            $response = Http::withHeaders([
                'Authorization' => $this->apiToken,
                'Content-Type' => 'application/json',
            ])->timeout(30);

            $url = $this->baseUrl . $endpoint;

            switch (strtoupper($method)) {
                case 'GET':
                    $httpResponse = $response->get($url, $data);
                    break;
                case 'POST':
                    $httpResponse = $response->post($url, $data);
                    break;
                case 'PUT':
                    $httpResponse = $response->put($url, $data);
                    break;
                case 'DELETE':
                    $httpResponse = $response->delete($url);
                    break;
                default:
                    throw new \InvalidArgumentException("Unsupported HTTP method: {$method}");
            }

            if ($httpResponse->successful()) {
                return [
                    'success' => true,
                    'data' => $httpResponse->json()
                ];
            }

            $errorData = $httpResponse->json();
            $errorMessage = $errorData['err'] ?? $errorData['error'] ?? 'Unknown API error';

            Log::warning('ClickUp API error', [
                'status' => $httpResponse->status(),
                'response' => $errorData,
                'endpoint' => $endpoint,
                'method' => $method
            ]);

            return [
                'success' => false,
                'message' => $errorMessage,
                'status_code' => $httpResponse->status()
            ];

        } catch (\Exception $e) {
            Log::error('ClickUp API request failed', [
                'method' => $method,
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Enforce rate limiting for API requests
     */
    private function enforceRateLimit(): void
    {
        $lastRequest = Cache::get('clickup_last_request', 0);
        $timeSinceLastRequest = microtime(true) - $lastRequest;
        
        if ($timeSinceLastRequest < $this->rateLimitDelay) {
            $sleepTime = $this->rateLimitDelay - $timeSinceLastRequest;
            usleep($sleepTime * 1000000); // Convert to microseconds
        }
        
        Cache::put('clickup_last_request', microtime(true), 60);
    }
}
