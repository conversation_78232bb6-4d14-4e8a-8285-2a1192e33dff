<?php

namespace App\Mail;

use App\Models\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ClickUpInsightsReport extends Mailable
{
    use Queueable, SerializesModels;

    public $product;
    public $progressData;
    public $startDate;
    public $endDate;
    public $customMessage;

    /**
     * Create a new message instance.
     */
    public function __construct(Product $product, array $progressData, string $startDate, string $endDate, ?string $customMessage = null)
    {
        $this->product = $product;
        $this->progressData = $progressData;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->customMessage = $customMessage;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'ClickUp Progress Report for ' . $this->product->name,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.clickup-insights-report',
            with: [
                'product' => $this->product,
                'progressData' => $this->progressData,
                'startDate' => $this->startDate,
                'endDate' => $this->endDate,
                'customMessage' => $this->customMessage,
                'overallStats' => $this->calculateOverallStats(),
            ]
        );
    }

    /**
     * Calculate overall statistics from progress data
     */
    private function calculateOverallStats(): array
    {
        $totalTasks = 0;
        $totalCompleted = 0;
        $totalInProgress = 0;
        $totalOpen = 0;
        $totalCompletionTime = 0;
        $listsWithCompletionTime = 0;

        foreach ($this->progressData as $list) {
            $progress = $list['progress'];
            $totalTasks += $progress['total_tasks'];
            $totalCompleted += $progress['completed_tasks'];
            $totalInProgress += $progress['in_progress_tasks'];
            $totalOpen += $progress['open_tasks'];
            
            if ($progress['avg_completion_time_days'] > 0) {
                $totalCompletionTime += $progress['avg_completion_time_days'];
                $listsWithCompletionTime++;
            }
        }

        $overallCompletionRate = $totalTasks > 0 ? round(($totalCompleted / $totalTasks) * 100, 1) : 0;
        $avgCompletionTime = $listsWithCompletionTime > 0 ? round($totalCompletionTime / $listsWithCompletionTime, 1) : 0;

        return [
            'total_tasks' => $totalTasks,
            'total_completed' => $totalCompleted,
            'total_in_progress' => $totalInProgress,
            'total_open' => $totalOpen,
            'completion_rate' => $overallCompletionRate,
            'avg_completion_time' => $avgCompletionTime,
            'total_lists' => count($this->progressData),
        ];
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
