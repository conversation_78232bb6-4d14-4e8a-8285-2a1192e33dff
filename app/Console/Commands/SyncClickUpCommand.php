<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SyncProductsWithClickUp;

class SyncClickUpCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clickup:sync {--force : Force sync even if integration is disabled}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize products with ClickUp tasks';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting ClickUp synchronization...');

        try {
            SyncProductsWithClickUp::dispatch();
            $this->info('ClickUp sync job dispatched successfully!');
        } catch (\Exception $e) {
            $this->error('Failed to dispatch sync job: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
