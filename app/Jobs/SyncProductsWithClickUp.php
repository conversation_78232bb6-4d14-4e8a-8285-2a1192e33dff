<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use App\Models\Product;
use App\Services\ClickUpService;

class SyncProductsWithClickUp implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $clickUpService = new ClickUpService();

        if (!$clickUpService->isEnabled()) {
            Log::info('ClickUp sync job skipped - integration not enabled');
            return;
        }

        Log::info('Starting ClickUp sync job');

        $products = Product::whereNotNull('clickup_task_id')->get();
        $syncCount = 0;
        $errorCount = 0;

        foreach ($products as $product) {
            try {
                $result = $clickUpService->getTask($product->clickup_task_id);

                if ($result['success']) {
                    $task = $result['task'];

                    // Update product with ClickUp data
                    $clickupData = $product->clickup_data ?? [];
                    $clickupData['status'] = $task['status']['status'] ?? null;
                    $clickupData['priority'] = $task['priority']['priority'] ?? null;
                    $clickupData['assignees'] = collect($task['assignees'] ?? [])->pluck('username')->toArray();
                    $clickupData['last_synced'] = now()->toISOString();

                    // Update completion date if task is completed
                    $updates = ['clickup_data' => $clickupData];
                    $newPhase = $clickUpService->mapStatusToPhase($task['status']['status'] ?? '');

                    if ($newPhase === 'done' && $product->phase !== 'done') {
                        $updates['phase'] = 'done';
                        $updates['completion_date'] = now()->toDateString();
                        Log::info("Product {$product->id} marked as completed from ClickUp sync");
                    }

                    $product->update($updates);
                    $syncCount++;
                } else {
                    Log::warning("Failed to sync product {$product->id}: {$result['message']}");
                    $errorCount++;
                }
            } catch (\Exception $e) {
                Log::error("Error syncing product {$product->id}: {$e->getMessage()}");
                $errorCount++;
            }

            // Rate limiting - small delay between requests
            usleep(500000); // 0.5 seconds
        }

        Log::info("ClickUp sync job completed", [
            'synced' => $syncCount,
            'errors' => $errorCount,
            'total' => $products->count()
        ]);
    }
}
