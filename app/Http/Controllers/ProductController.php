<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\TeamMember;
use App\Services\ClickUpService;

class ProductController extends Controller
{
    private $clickUpService;

    public function __construct(ClickUpService $clickUpService)
    {
        $this->clickUpService = $clickUpService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Product::with('teamMembers');

        // Filter by phase
        if ($request->filled('phase')) {
            $query->where('phase', $request->phase);
        }

        // Search by name
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $products = $query->latest()->paginate(10);

        if ($request->ajax()) {
            return response()->json([
                'html' => view('products.partials.table', compact('products'))->render(),
                'pagination' => $products->links()->render()
            ]);
        }

        return view('products.index', compact('products'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Check if ClickUp integration is properly configured
        if (!$this->clickUpService->isEnabled()) {
            return redirect()->route('settings.index')
                ->with('error', 'ClickUp integration must be configured before creating products. Please configure ClickUp settings first.');
        }

        $teamMembers = TeamMember::where('is_active', true)->get();
        return view('products.create', compact('teamMembers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Debug logging
        \Log::info('Product store request data:', $request->all());

        // Check if ClickUp integration is properly configured
        if (!$this->clickUpService->isEnabled()) {
            return response()->json([
                'success' => false,
                'message' => 'ClickUp integration must be configured before creating products. Please configure ClickUp settings first.',
                'redirect' => route('settings.index')
            ], 422);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'phase' => 'required|in:idea,in_progress,done',
            'start_date' => 'nullable|date',
            'target_date' => 'nullable|date|after_or_equal:start_date',
            'completion_date' => 'nullable|date',
            'team_members' => 'nullable|array',
            'team_members.*' => 'nullable|exists:team_members,id',
            'roles' => 'nullable|array',
        ]);

        // Filter out empty team members and roles
        if (isset($validated['team_members'])) {
            $validated['team_members'] = array_filter($validated['team_members'], function($value) {
                return !empty($value);
            });
        }

        if (isset($validated['roles'])) {
            $validated['roles'] = array_filter($validated['roles'], function($value) {
                return !empty($value);
            });
        }

        $product = Product::create($validated);

        // Attach team members with roles
        if (!empty($validated['team_members'])) {
            $teamMemberData = [];
            foreach ($validated['team_members'] as $index => $memberId) {
                $teamMemberData[$memberId] = [
                    'role_in_product' => $validated['roles'][$index] ?? null
                ];
            }
            $product->teamMembers()->attach($teamMemberData);
        }

        // Create ClickUp task if integration is enabled
        if ($this->clickUpService->isEnabled()) {
            $this->createClickUpTask($product);
        }

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Product created successfully!',
                'redirect' => route('products.index')
            ]);
        }

        return redirect()->route('products.index')->with('success', 'Product created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        $product->load(['teamMembers', 'currentDocuments.uploader', 'clickupLists']);
        return view('products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        $teamMembers = TeamMember::where('is_active', true)->get();
        $product->load(['teamMembers', 'clickupLists']);
        return view('products.edit', compact('product', 'teamMembers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        // Check if ClickUp integration is properly configured
        if (!$this->clickUpService->isEnabled()) {
            return response()->json([
                'success' => false,
                'message' => 'ClickUp integration must be configured before updating products. Please configure ClickUp settings first.',
                'redirect' => route('settings.index')
            ], 422);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'phase' => 'required|in:idea,in_progress,done',
            'start_date' => 'nullable|date',
            'target_date' => 'nullable|date|after_or_equal:start_date',
            'completion_date' => 'nullable|date',
            'team_members' => 'nullable|array',
            'team_members.*' => 'nullable|exists:team_members,id',
            'roles' => 'nullable|array',
        ]);

        // Filter out empty team members and roles
        if (isset($validated['team_members'])) {
            $validated['team_members'] = array_filter($validated['team_members'], function($value) {
                return !empty($value);
            });
        }

        if (isset($validated['roles'])) {
            $validated['roles'] = array_filter($validated['roles'], function($value) {
                return !empty($value);
            });
        }

        $originalPhase = $product->phase;
        $product->update($validated);

        // Sync team members with roles
        if (isset($validated['team_members'])) {
            $teamMemberData = [];
            foreach ($validated['team_members'] as $index => $memberId) {
                $teamMemberData[$memberId] = [
                    'role_in_product' => $validated['roles'][$index] ?? null
                ];
            }
            $product->teamMembers()->sync($teamMemberData);
        } else {
            $product->teamMembers()->detach();
        }

        // Update ClickUp task if integration is enabled and phase changed
        if ($this->clickUpService->isEnabled() && $product->clickup_task_id) {
            if ($originalPhase !== $product->phase) {
                $this->updateClickUpTask($product);
            }
        }

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Product updated successfully!',
                'redirect' => route('products.index')
            ]);
        }

        return redirect()->route('products.index')->with('success', 'Product updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        $product->teamMembers()->detach();
        $product->delete();

        return response()->json([
            'success' => true,
            'message' => 'Product deleted successfully!'
        ]);
    }

    /**
     * Sync product with ClickUp
     */
    public function syncWithClickUp(Product $product)
    {
        if (!$this->clickUpService->isEnabled()) {
            return response()->json([
                'success' => false,
                'message' => 'ClickUp integration is not enabled'
            ]);
        }

        try {
            if ($product->clickup_task_id) {
                // Update existing task
                $result = $this->updateClickUpTask($product);
            } else {
                // Create new task
                $result = $this->createClickUpTask($product);
            }

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Product synced with ClickUp successfully!'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to sync with ClickUp'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error syncing with ClickUp: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Create ClickUp task for product
     */
    private function createClickUpTask(Product $product): bool
    {
        $taskData = [
            'name' => $product->name,
            'description' => $product->description ?? '',
            'phase' => $product->phase,
            'due_date' => $product->target_date?->format('Y-m-d'),
            'start_date' => $product->start_date?->format('Y-m-d'),
        ];

        // Use assigned ClickUp list if available
        $result = $this->clickUpService->createTask($taskData, $product->clickup_list_id);

        if ($result['success']) {
            $updateData = [
                'clickup_task_id' => $result['task']['id'],
                'clickup_data' => [
                    'url' => $result['task']['url'] ?? null,
                    'status' => $result['task']['status']['status'] ?? null,
                    'created_at' => now()->toISOString(),
                    'last_synced' => now()->toISOString(),
                ]
            ];

            // If list was assigned, store list information
            if ($product->clickup_list_id) {
                $listResult = $this->clickUpService->getList($product->clickup_list_id);
                if ($listResult['success']) {
                    $updateData['clickup_list_data'] = [
                        'name' => $listResult['list']['name'] ?? null,
                        'space' => $listResult['list']['space']['name'] ?? null,
                        'last_synced' => now()->toISOString(),
                    ];
                }
            }

            $product->update($updateData);
            return true;
        }

        return false;
    }

    /**
     * Update ClickUp task for product
     */
    private function updateClickUpTask(Product $product): bool
    {
        $updates = [
            'name' => $product->name,
            'description' => $product->description ?? '',
            'phase' => $product->phase,
            'due_date' => $product->target_date?->format('Y-m-d'),
        ];

        $result = $this->clickUpService->updateTask($product->clickup_task_id, $updates);

        if ($result['success']) {
            $clickupData = $product->clickup_data ?? [];
            $clickupData['last_synced'] = now()->toISOString();
            $clickupData['status'] = $result['task']['status']['status'] ?? $clickupData['status'] ?? null;

            $product->update(['clickup_data' => $clickupData]);
            return true;
        }

        return false;
    }

    /**
     * Refresh ClickUp data for product
     */
    public function refreshClickUpData(Product $product)
    {
        if (!$this->clickUpService->isEnabled() || !$product->clickup_task_id) {
            return response()->json([
                'success' => false,
                'message' => 'ClickUp integration not available for this product'
            ]);
        }

        $result = $this->clickUpService->getTask($product->clickup_task_id);

        if ($result['success']) {
            $task = $result['task'];

            // Update product with ClickUp data
            $clickupData = [
                'url' => $task['url'] ?? null,
                'status' => $task['status']['status'] ?? null,
                'priority' => $task['priority']['priority'] ?? null,
                'assignees' => collect($task['assignees'] ?? [])->pluck('username')->toArray(),
                'last_synced' => now()->toISOString(),
            ];

            // Update completion date if task is completed
            $updates = ['clickup_data' => $clickupData];
            $newPhase = $this->clickUpService->mapStatusToPhase($task['status']['status'] ?? '');

            if ($newPhase === 'done' && $product->phase !== 'done') {
                $updates['phase'] = 'done';
                $updates['completion_date'] = now()->toDateString();
            }

            $product->update($updates);

            return response()->json([
                'success' => true,
                'message' => 'ClickUp data refreshed successfully!',
                'data' => $clickupData
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to refresh ClickUp data'
        ]);
    }

    /**
     * Add a ClickUp list assignment to a product
     */
    public function addClickupList(Request $request, Product $product)
    {
        $validated = $request->validate([
            'clickup_list_id' => 'required|string',
            'list_type' => 'required|in:features,bugs,other',
        ]);

        // Check if this list is already assigned to the product
        $existingAssignment = $product->clickupLists()
            ->where('clickup_list_id', $validated['clickup_list_id'])
            ->first();

        if ($existingAssignment) {
            return response()->json([
                'success' => false,
                'message' => 'This ClickUp list is already assigned to this product.'
            ], 422);
        }

        try {
            // Get list information from ClickUp
            $listResult = $this->clickUpService->getList($validated['clickup_list_id']);

            $listData = null;
            if ($listResult['success']) {
                $listData = [
                    'name' => $listResult['list']['name'] ?? null,
                    'space' => $listResult['list']['space']['name'] ?? null,
                    'folder' => $listResult['list']['folder']['name'] ?? null,
                    'last_synced' => now()->toISOString(),
                ];
            }

            // Create the assignment
            $assignment = $product->clickupLists()->create([
                'clickup_list_id' => $validated['clickup_list_id'],
                'list_type' => $validated['list_type'],
                'clickup_list_data' => $listData,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'ClickUp list assigned successfully!',
                'assignment' => [
                    'id' => $assignment->id,
                    'list_type_name' => $assignment->list_type_name,
                    'list_name' => $listData['name'] ?? 'ClickUp List',
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error assigning ClickUp list: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove a ClickUp list assignment from a product
     */
    public function removeClickupList(Product $product, $assignmentId)
    {
        try {
            $assignment = $product->clickupLists()->findOrFail($assignmentId);
            $assignment->delete();

            return response()->json([
                'success' => true,
                'message' => 'ClickUp list assignment removed successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error removing ClickUp list assignment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send ClickUp insights email report
     */
    public function sendInsightsReport(Request $request, Product $product)
    {
        $validated = $request->validate([
            'recipients' => 'required|array|min:1',
            'recipients.*' => 'required|email',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'list_type_filter' => 'nullable|in:features,bugs,other',
            'custom_message' => 'nullable|string|max:1000',
        ]);

        if (!$this->clickUpService->isEnabled()) {
            return response()->json([
                'success' => false,
                'message' => 'ClickUp integration is not enabled'
            ]);
        }

        try {
            // Get progress data
            $progressData = [];
            $assignments = $product->clickupLists();

            if (!empty($validated['list_type_filter'])) {
                $assignments = $assignments->where('list_type', $validated['list_type_filter']);
            }

            $assignments = $assignments->get();

            foreach ($assignments as $assignment) {
                $listProgress = $this->clickUpService->getListTasksProgress(
                    $assignment->clickup_list_id,
                    $validated['start_date'],
                    $validated['end_date']
                );

                if ($listProgress['success']) {
                    // Build hierarchical path
                    $hierarchicalPath = '';
                    if (isset($assignment->clickup_list_data['space'])) {
                        $hierarchicalPath = $assignment->clickup_list_data['space'];

                        if (isset($assignment->clickup_list_data['folder']) && !empty($assignment->clickup_list_data['folder'])) {
                            $hierarchicalPath .= ' › ' . $assignment->clickup_list_data['folder'];
                        } else {
                            $hierarchicalPath .= ' › No Folder';
                        }

                        $hierarchicalPath .= ' › ' . ($assignment->clickup_list_data['name'] ?? 'List');
                    } else {
                        $hierarchicalPath = $assignment->clickup_list_data['name'] ?? 'ClickUp List';
                    }

                    $progressData[] = [
                        'assignment_id' => $assignment->id,
                        'list_id' => $assignment->clickup_list_id,
                        'list_name' => $assignment->clickup_list_data['name'] ?? 'Unknown List',
                        'list_type' => $assignment->list_type,
                        'list_type_name' => $assignment->list_type_name,
                        'hierarchical_path' => $hierarchicalPath,
                        'progress' => $listProgress['data']
                    ];
                }
            }

            // Send email
            \Mail::to($validated['recipients'])->send(new \App\Mail\ClickUpInsightsReport(
                $product,
                $progressData,
                $validated['start_date'],
                $validated['end_date'],
                $validated['custom_message'] ?? null
            ));

            return response()->json([
                'success' => true,
                'message' => 'Insights report sent successfully to ' . count($validated['recipients']) . ' recipient(s)!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending insights report: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get ClickUp progress data for a product within a date range
     */
    public function getClickUpProgress(Request $request, Product $product)
    {
        $validated = $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'list_type_filter' => 'nullable|in:features,bugs,other',
        ]);

        if (!$this->clickUpService->isEnabled()) {
            return response()->json([
                'success' => false,
                'message' => 'ClickUp integration is not enabled'
            ]);
        }

        try {
            $progressData = [];
            $assignments = $product->clickupLists();

            // Filter by list type if provided
            if (!empty($validated['list_type_filter'])) {
                $assignments = $assignments->where('list_type', $validated['list_type_filter']);
            }

            $assignments = $assignments->get();

            foreach ($assignments as $assignment) {
                $listProgress = $this->clickUpService->getListTasksProgress(
                    $assignment->clickup_list_id,
                    $validated['start_date'],
                    $validated['end_date']
                );

                if ($listProgress['success']) {
                    // Build hierarchical path
                    $hierarchicalPath = '';
                    if (isset($assignment->clickup_list_data['space'])) {
                        $hierarchicalPath = $assignment->clickup_list_data['space'];

                        if (isset($assignment->clickup_list_data['folder']) && !empty($assignment->clickup_list_data['folder'])) {
                            $hierarchicalPath .= ' › ' . $assignment->clickup_list_data['folder'];
                        } else {
                            $hierarchicalPath .= ' › No Folder';
                        }

                        $hierarchicalPath .= ' › ' . ($assignment->clickup_list_data['name'] ?? 'List');
                    } else {
                        $hierarchicalPath = $assignment->clickup_list_data['name'] ?? 'ClickUp List';
                    }

                    $progressData[] = [
                        'assignment_id' => $assignment->id,
                        'list_id' => $assignment->clickup_list_id,
                        'list_name' => $assignment->clickup_list_data['name'] ?? 'Unknown List',
                        'list_type' => $assignment->list_type,
                        'list_type_name' => $assignment->list_type_name,
                        'hierarchical_path' => $hierarchicalPath,
                        'progress' => $listProgress['data']
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'data' => $progressData,
                'date_range' => [
                    'start_date' => $validated['start_date'],
                    'end_date' => $validated['end_date']
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching ClickUp progress data: ' . $e->getMessage()
            ], 500);
        }
    }
}
