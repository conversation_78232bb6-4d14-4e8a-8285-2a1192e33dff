<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Kpi;
use App\Models\TeamMember;
use App\Models\Evaluation;

class KpiController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Kpi::with(['teamMember', 'evaluations']);

        // Filter by team member
        if ($request->filled('team_member')) {
            $query->where('team_member_id', $request->team_member);
        }

        // Filter by status
        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->where('is_active', $isActive);
        }

        // Search by name
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $kpis = $query->latest()->paginate(10);
        $teamMembers = TeamMember::where('is_active', true)->get();

        if ($request->ajax()) {
            return response()->json([
                'html' => view('kpis.partials.table', compact('kpis'))->render(),
                'pagination' => $kpis->links()->render()
            ]);
        }

        return view('kpis.index', compact('kpis', 'teamMembers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $teamMembers = TeamMember::where('is_active', true)->get();
        $selectedTeamMember = $request->get('team_member');

        return view('kpis.create', compact('teamMembers', 'selectedTeamMember'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'team_member_id' => 'required|exists:team_members,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'metric_type' => 'required|string|max:255',
            'target_value' => 'required|numeric|min:0',
            'period' => 'required|in:weekly,monthly,quarterly',
            'is_active' => 'nullable|boolean',
        ]);

        $validated['is_active'] = $request->has('is_active');

        $kpi = Kpi::create($validated);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'KPI created successfully!',
                'redirect' => route('kpis.index')
            ]);
        }

        return redirect()->route('kpis.index')->with('success', 'KPI created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Kpi $kpi)
    {
        $kpi->load(['teamMember', 'evaluations' => function($query) {
            $query->latest('evaluation_date');
        }]);

        return view('kpis.show', compact('kpi'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Kpi $kpi)
    {
        $teamMembers = TeamMember::where('is_active', true)->get();
        return view('kpis.edit', compact('kpi', 'teamMembers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Kpi $kpi)
    {
        $validated = $request->validate([
            'team_member_id' => 'required|exists:team_members,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'metric_type' => 'required|string|max:255',
            'target_value' => 'required|numeric|min:0',
            'period' => 'required|in:weekly,monthly,quarterly',
            'is_active' => 'nullable|boolean',
        ]);

        $validated['is_active'] = $request->has('is_active');

        $kpi->update($validated);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'KPI updated successfully!',
                'redirect' => route('kpis.index')
            ]);
        }

        return redirect()->route('kpis.index')->with('success', 'KPI updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Kpi $kpi)
    {
        $kpi->evaluations()->delete();
        $kpi->delete();

        return response()->json([
            'success' => true,
            'message' => 'KPI deleted successfully!'
        ]);
    }
}
