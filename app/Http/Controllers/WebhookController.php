<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\Product;
use App\Services\ClickUpService;

class WebhookController extends Controller
{
    /**
     * Handle ClickUp webhook
     */
    public function clickup(Request $request)
    {
        Log::info('ClickUp webhook received', $request->all());

        try {
            $payload = $request->all();

            // Verify webhook signature if needed
            // $this->verifyWebhookSignature($request);

            $event = $payload['event'] ?? null;
            $taskId = $payload['task_id'] ?? null;

            if (!$taskId) {
                Log::warning('ClickUp webhook missing task_id');
                return response()->json(['status' => 'error', 'message' => 'Missing task_id'], 400);
            }

            // Find product by ClickUp task ID
            $product = Product::where('clickup_task_id', $taskId)->first();

            if (!$product) {
                Log::info("No product found for ClickUp task {$taskId}");
                return response()->json(['status' => 'ok', 'message' => 'Task not tracked']);
            }

            // Handle different webhook events
            switch ($event) {
                case 'taskStatusUpdated':
                    $this->handleTaskStatusUpdate($product, $payload);
                    break;

                case 'taskUpdated':
                    $this->handleTaskUpdate($product, $payload);
                    break;

                case 'taskDeleted':
                    $this->handleTaskDeleted($product, $payload);
                    break;

                default:
                    Log::info("Unhandled ClickUp webhook event: {$event}");
            }

            return response()->json(['status' => 'ok']);

        } catch (\Exception $e) {
            Log::error('ClickUp webhook error: ' . $e->getMessage());
            return response()->json(['status' => 'error', 'message' => 'Internal error'], 500);
        }
    }

    /**
     * Handle task status update
     */
    private function handleTaskStatusUpdate(Product $product, array $payload): void
    {
        $newStatus = $payload['history_items'][0]['after']['status'] ?? null;

        if (!$newStatus) {
            return;
        }

        $clickUpService = new ClickUpService();
        $newPhase = $clickUpService->mapStatusToPhase($newStatus);

        $updates = [];
        $clickupData = $product->clickup_data ?? [];
        $clickupData['status'] = $newStatus;
        $clickupData['last_synced'] = now()->toISOString();
        $updates['clickup_data'] = $clickupData;

        // Update phase if it changed
        if ($newPhase !== $product->phase) {
            $updates['phase'] = $newPhase;

            // Set completion date if task is completed
            if ($newPhase === 'done' && !$product->completion_date) {
                $updates['completion_date'] = now()->toDateString();
            }
        }

        $product->update($updates);

        Log::info("Product {$product->id} updated from ClickUp webhook", [
            'old_phase' => $product->phase,
            'new_phase' => $newPhase,
            'clickup_status' => $newStatus
        ]);
    }

    /**
     * Handle general task update
     */
    private function handleTaskUpdate(Product $product, array $payload): void
    {
        $clickupData = $product->clickup_data ?? [];
        $clickupData['last_synced'] = now()->toISOString();

        // Update any changed fields from the webhook
        if (isset($payload['task']['name']) && $payload['task']['name'] !== $product->name) {
            // Optionally sync name changes back to product
            // $product->update(['name' => $payload['task']['name']]);
        }

        $product->update(['clickup_data' => $clickupData]);

        Log::info("Product {$product->id} general update from ClickUp webhook");
    }

    /**
     * Handle task deletion
     */
    private function handleTaskDeleted(Product $product, array $payload): void
    {
        $product->update([
            'clickup_task_id' => null,
            'clickup_data' => null
        ]);

        Log::info("Product {$product->id} ClickUp task deleted via webhook");
    }

    /**
     * Verify webhook signature (implement if ClickUp provides signature verification)
     */
    private function verifyWebhookSignature(Request $request): bool
    {
        // Implement signature verification if needed
        // $signature = $request->header('X-Signature');
        // $payload = $request->getContent();
        // $expectedSignature = hash_hmac('sha256', $payload, config('clickup.webhook_secret'));
        // return hash_equals($signature, $expectedSignature);

        return true;
    }
}
