<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Setting;
use App\Services\ClickUpService;

class SettingsController extends Controller
{
    private $clickUpService;

    public function __construct(ClickUpService $clickUpService)
    {
        $this->clickUpService = $clickUpService;
    }

    /**
     * Display the settings page
     */
    public function index()
    {
        $settings = [
            'clickup_api_token' => Setting::get('clickup_api_token', ''),
            'clickup_team_id' => Setting::get('clickup_team_id', ''),
            'clickup_space_id' => Setting::get('clickup_space_id', ''),
            'clickup_sync_enabled' => Setting::get('clickup_sync_enabled', false),
            'gemini_api_key' => Setting::get('gemini_api_key', ''),
            'gemini_ai_enabled' => Setting::get('gemini_ai_enabled', false),
            'app_name' => Setting::get('app_name', 'Taqnyat Product Manager'),
            'reports_retention_days' => Setting::get('reports_retention_days', 365),
        ];

        // Test ClickUp connection if credentials are provided
        $clickupStatus = null;
        if (!empty($settings['clickup_api_token'])) {
            $clickupStatus = $this->clickUpService->testConnection();
        }

        return view('settings.index', compact('settings', 'clickupStatus'));
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'clickup_api_token' => 'nullable|string',
            'clickup_team_id' => 'nullable|string',
            'clickup_space_id' => 'nullable|string',
            'clickup_sync_enabled' => 'boolean',
            'gemini_api_key' => 'nullable|string',
            'gemini_ai_enabled' => 'boolean',
            'app_name' => 'required|string|max:255',
            'reports_retention_days' => 'required|integer|min:1|max:3650',
        ]);

        // Handle checkboxes
        $validated['clickup_sync_enabled'] = $request->has('clickup_sync_enabled');
        $validated['gemini_ai_enabled'] = $request->has('gemini_ai_enabled');

        // Save each setting
        foreach ($validated as $key => $value) {
            $type = match($key) {
                'clickup_sync_enabled', 'gemini_ai_enabled' => 'boolean',
                'reports_retention_days' => 'integer',
                default => 'string'
            };

            Setting::set($key, $value, $type);
        }

        return redirect()->route('settings.index')->with('success', 'Settings updated successfully!');
    }

    /**
     * Test ClickUp connection
     */
    public function testClickUpConnection(Request $request)
    {
        $apiToken = $request->input('api_token');

        if (empty($apiToken)) {
            return response()->json([
                'success' => false,
                'message' => 'API token is required'
            ]);
        }

        // Create a new service instance with the test token
        $testService = new \App\Services\ClickUpService();
        $testService->setApiToken($apiToken);

        $result = $testService->testConnection();

        return response()->json($result);
    }

    /**
     * Get ClickUp teams
     */
    public function getClickUpTeams(Request $request)
    {
        $apiToken = $request->input('api_token') ?? Setting::get('clickup_api_token');

        if (empty($apiToken)) {
            return response()->json([
                'success' => false,
                'message' => 'API token is required'
            ]);
        }

        $testService = new \App\Services\ClickUpService();
        $testService->setApiToken($apiToken);

        $result = $testService->getTeams();
        return response()->json($result);
    }

    /**
     * Get ClickUp spaces for a team
     */
    public function getClickUpSpaces(Request $request)
    {
        $teamId = $request->input('team_id') ?? Setting::get('clickup_team_id');
        $apiToken = $request->input('api_token') ?? Setting::get('clickup_api_token');

        if (empty($teamId)) {
            return response()->json([
                'success' => false,
                'message' => 'Team ID is required. Please configure ClickUp settings first.'
            ]);
        }

        if (empty($apiToken)) {
            return response()->json([
                'success' => false,
                'message' => 'API token is required'
            ]);
        }

        $testService = new \App\Services\ClickUpService();
        $testService->setApiToken($apiToken);
        $testService->setTeamId($teamId);

        $result = $testService->getSpaces();

        return response()->json($result);
    }

    /**
     * Get ClickUp folders for a space
     */
    public function getClickUpFolders(Request $request)
    {
        $spaceId = $request->input('space_id');
        $apiToken = $request->input('api_token') ?? Setting::get('clickup_api_token');

        if (empty($spaceId)) {
            return response()->json([
                'success' => false,
                'message' => 'Space ID is required'
            ]);
        }

        if (empty($apiToken)) {
            return response()->json([
                'success' => false,
                'message' => 'API token is required'
            ]);
        }

        $testService = new \App\Services\ClickUpService();
        $testService->setApiToken($apiToken);

        $result = $testService->getFolders($spaceId);

        return response()->json($result);
    }

    /**
     * Get ClickUp lists for a space or folder
     */
    public function getClickUpLists(Request $request)
    {
        $spaceId = $request->input('space_id');
        $folderId = $request->input('folder_id');
        $apiToken = $request->input('api_token') ?? Setting::get('clickup_api_token');

        if (empty($spaceId) && empty($folderId)) {
            return response()->json([
                'success' => false,
                'message' => 'Space ID or Folder ID is required'
            ]);
        }

        if (empty($apiToken)) {
            return response()->json([
                'success' => false,
                'message' => 'API token is required'
            ]);
        }

        $testService = new \App\Services\ClickUpService();
        $testService->setApiToken($apiToken);

        if ($folderId) {
            // Get lists from specific folder
            $result = $testService->getListsFromFolder($folderId);
        } else {
            // Get folderless lists from space
            $result = $testService->getLists($spaceId);
        }

        return response()->json($result);
    }
}
