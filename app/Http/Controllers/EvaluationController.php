<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Evaluation;
use App\Models\Kpi;

class EvaluationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Evaluation::with(['kpi.teamMember']);

        if ($request->filled('kpi')) {
            $query->where('kpi_id', $request->kpi);
        }

        $evaluations = $query->latest('evaluation_date')->paginate(10);
        $kpis = Kpi::with('teamMember')->where('is_active', true)->get();

        return view('evaluations.index', compact('evaluations', 'kpis'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $kpis = Kpi::with('teamMember')->where('is_active', true)->get();
        $selectedKpi = $request->get('kpi');

        return view('evaluations.create', compact('kpis', 'selectedKpi'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'kpi_id' => 'required|exists:kpis,id',
            'actual_value' => 'required|numeric|min:0',
            'evaluation_date' => 'required|date',
            'comments' => 'nullable|string',
            'recommendations' => 'nullable|string',
        ]);

        // Determine status based on actual vs target
        $kpi = Kpi::find($validated['kpi_id']);
        $actualValue = $validated['actual_value'];
        $targetValue = $kpi->target_value;

        if ($actualValue < $targetValue * 0.8) {
            $status = 'below_target';
        } elseif ($actualValue > $targetValue * 1.1) {
            $status = 'above_target';
        } else {
            $status = 'on_target';
        }

        $validated['status'] = $status;

        Evaluation::create($validated);

        return redirect()->route('evaluations.index')->with('success', 'Evaluation created successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Evaluation $evaluation)
    {
        $evaluation->delete();

        return response()->json([
            'success' => true,
            'message' => 'Evaluation deleted successfully!'
        ]);
    }
}
