<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TeamMember;
use App\Models\Product;
use App\Services\ClickUpService;

class TeamMemberController extends Controller
{
    protected $clickUpService;

    public function __construct(ClickUpService $clickUpService)
    {
        $this->clickUpService = $clickUpService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = TeamMember::with(['products', 'kpis', 'activeClickupLists']);

        // Filter by status
        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->where('is_active', $isActive);
        }

        // Search by name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                  ->orWhere('email', 'like', '%' . $search . '%')
                  ->orWhere('role', 'like', '%' . $search . '%');
            });
        }

        $teamMembers = $query->latest()->paginate(10);

        if ($request->ajax()) {
            return response()->json([
                'html' => view('team-members.partials.table', compact('teamMembers'))->render(),
                'pagination' => $teamMembers->links()->render()
            ]);
        }

        return view('team-members.index', compact('teamMembers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $products = Product::all();

        // Get ClickUp spaces for cascading dropdown
        $clickupSpaces = [];
        if ($this->clickUpService->isEnabled()) {
            $spacesResult = $this->clickUpService->getSpaces();
            if ($spacesResult['success']) {
                $clickupSpaces = $spacesResult['spaces'];
            }
        }

        return view('team-members.create', compact('products', 'clickupSpaces'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Debug logging
        \Log::info('Team member store request data:', $request->all());

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:team_members,email',
            'role' => 'nullable|string|max:255',
            'responsibilities' => 'nullable|string',
            'is_active' => 'nullable|boolean',
            'products' => 'nullable|array',
            'products.*' => 'nullable|exists:products,id',
            'product_roles' => 'nullable|array',
            // ClickUp list assignments
            'clickup_lists' => 'nullable|array',
            'clickup_lists.*' => 'nullable|string',
            'clickup_list_types' => 'nullable|array',
            'clickup_list_types.*' => 'nullable|in:features,bugs,other',
            'clickup_notes' => 'nullable|array',
        ]);

        // Filter out empty products and roles
        if (isset($validated['products'])) {
            $validated['products'] = array_filter($validated['products'], function($value) {
                return !empty($value);
            });
        }

        if (isset($validated['product_roles'])) {
            $validated['product_roles'] = array_filter($validated['product_roles'], function($value) {
                return !empty($value);
            });
        }

        $validated['is_active'] = $request->has('is_active');

        $teamMember = TeamMember::create($validated);

        // Attach products with roles
        if (!empty($validated['products'])) {
            $productData = [];
            foreach ($validated['products'] as $index => $productId) {
                $productData[$productId] = [
                    'role_in_product' => $validated['product_roles'][$index] ?? null
                ];
            }
            $teamMember->products()->attach($productData);
        }

        // Attach ClickUp lists
        if (!empty($validated['clickup_lists'])) {
            $this->attachClickupLists($teamMember, $validated);
        }

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Team member created successfully!',
                'redirect' => route('team-members.index')
            ]);
        }

        return redirect()->route('team-members.index')->with('success', 'Team member created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(TeamMember $teamMember)
    {
        $teamMember->load(['products', 'kpis.evaluations']);
        return view('team-members.show', compact('teamMember'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TeamMember $teamMember)
    {
        $products = Product::all();

        // Get ClickUp spaces for cascading dropdown
        $clickupSpaces = [];
        if ($this->clickUpService->isEnabled()) {
            $spacesResult = $this->clickUpService->getSpaces();
            if ($spacesResult['success']) {
                $clickupSpaces = $spacesResult['spaces'];
            }
        }

        // Load existing relationships
        $teamMember->load(['products', 'activeClickupLists']);

        return view('team-members.edit', compact('teamMember', 'products', 'clickupSpaces'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TeamMember $teamMember)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:team_members,email,' . $teamMember->id,
            'role' => 'nullable|string|max:255',
            'responsibilities' => 'nullable|string',
            'is_active' => 'nullable|boolean',
            'products' => 'nullable|array',
            'products.*' => 'nullable|exists:products,id',
            'product_roles' => 'nullable|array',
            // ClickUp list assignments
            'clickup_lists' => 'nullable|array',
            'clickup_lists.*' => 'nullable|string',
            'clickup_list_types' => 'nullable|array',
            'clickup_list_types.*' => 'nullable|in:features,bugs,other',
            'clickup_notes' => 'nullable|array',
        ]);

        // Filter out empty products and roles
        if (isset($validated['products'])) {
            $validated['products'] = array_filter($validated['products'], function($value) {
                return !empty($value);
            });
        }

        if (isset($validated['product_roles'])) {
            $validated['product_roles'] = array_filter($validated['product_roles'], function($value) {
                return !empty($value);
            });
        }

        $validated['is_active'] = $request->has('is_active');

        $teamMember->update($validated);

        // Sync products with roles
        if (isset($validated['products'])) {
            $productData = [];
            foreach ($validated['products'] as $index => $productId) {
                $productData[$productId] = [
                    'role_in_product' => $validated['product_roles'][$index] ?? null
                ];
            }
            $teamMember->products()->sync($productData);
        } else {
            $teamMember->products()->detach();
        }

        // Sync ClickUp lists
        $this->syncClickupLists($teamMember, $validated);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Team member updated successfully!',
                'redirect' => route('team-members.index')
            ]);
        }

        return redirect()->route('team-members.index')->with('success', 'Team member updated successfully!');
    }

    /**
     * Toggle team member status
     */
    public function toggleStatus(Request $request, TeamMember $teamMember)
    {
        $teamMember->update([
            'is_active' => $request->boolean('is_active')
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Status updated successfully!'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TeamMember $teamMember)
    {
        $teamMember->products()->detach();
        $teamMember->clickupLists()->delete();
        $teamMember->delete();

        return response()->json([
            'success' => true,
            'message' => 'Team member deleted successfully!'
        ]);
    }

    /**
     * Attach ClickUp lists to team member
     */
    private function attachClickupLists(TeamMember $teamMember, array $validated): void
    {
        if (empty($validated['clickup_lists'])) {
            return;
        }

        foreach ($validated['clickup_lists'] as $index => $listId) {
            if (empty($listId)) {
                continue;
            }

            $listType = $validated['clickup_list_types'][$index] ?? 'other';
            $notes = $validated['clickup_notes'][$index] ?? null;

            // Get list information from ClickUp
            $listResult = $this->clickUpService->getList($listId);

            $listData = null;
            $spaceData = null;
            $folderData = null;

            if ($listResult['success']) {
                $list = $listResult['list'];

                $listData = [
                    'name' => $list['name'] ?? null,
                    'last_synced' => now()->toISOString(),
                ];

                if (isset($list['space'])) {
                    $spaceData = [
                        'id' => $list['space']['id'] ?? null,
                        'name' => $list['space']['name'] ?? null,
                    ];
                }

                if (isset($list['folder']) && !empty($list['folder']['name'])) {
                    $folderData = [
                        'id' => $list['folder']['id'] ?? null,
                        'name' => $list['folder']['name'] ?? null,
                    ];
                }
            }

            // Create the assignment
            $teamMember->clickupLists()->create([
                'clickup_list_id' => $listId,
                'list_type' => $listType,
                'clickup_list_data' => $listData,
                'clickup_space_data' => $spaceData,
                'clickup_folder_data' => $folderData,
                'notes' => $notes,
                'is_active' => true,
            ]);
        }
    }

    /**
     * Sync ClickUp lists for team member
     */
    private function syncClickupLists(TeamMember $teamMember, array $validated): void
    {
        // Remove all existing assignments
        $teamMember->clickupLists()->delete();

        // Add new assignments
        $this->attachClickupLists($teamMember, $validated);
    }

    /**
     * Add a ClickUp list assignment to a team member
     */
    public function addClickupList(Request $request, TeamMember $teamMember)
    {
        $validated = $request->validate([
            'clickup_list_id' => 'required|string',
            'list_type' => 'required|in:features,bugs,other',
            'notes' => 'nullable|string',
        ]);

        // Check if this list is already assigned to the team member
        $existingAssignment = $teamMember->clickupLists()
            ->where('clickup_list_id', $validated['clickup_list_id'])
            ->first();

        if ($existingAssignment) {
            return response()->json([
                'success' => false,
                'message' => 'This ClickUp list is already assigned to this team member.'
            ], 422);
        }

        try {
            // Get list information from ClickUp
            $listResult = $this->clickUpService->getList($validated['clickup_list_id']);

            $listData = null;
            $spaceData = null;
            $folderData = null;

            if ($listResult['success']) {
                $list = $listResult['list'];

                $listData = [
                    'name' => $list['name'] ?? null,
                    'last_synced' => now()->toISOString(),
                ];

                if (isset($list['space'])) {
                    $spaceData = [
                        'id' => $list['space']['id'] ?? null,
                        'name' => $list['space']['name'] ?? null,
                    ];
                }

                if (isset($list['folder']) && !empty($list['folder']['name'])) {
                    $folderData = [
                        'id' => $list['folder']['id'] ?? null,
                        'name' => $list['folder']['name'] ?? null,
                    ];
                }
            }

            // Create the assignment
            $assignment = $teamMember->clickupLists()->create([
                'clickup_list_id' => $validated['clickup_list_id'],
                'list_type' => $validated['list_type'],
                'clickup_list_data' => $listData,
                'clickup_space_data' => $spaceData,
                'clickup_folder_data' => $folderData,
                'notes' => $validated['notes'],
                'is_active' => true,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'ClickUp list assigned successfully!',
                'assignment' => $assignment->load('teamMember')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error assigning ClickUp list: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove a ClickUp list assignment from a team member
     */
    public function removeClickupList(TeamMember $teamMember, $assignmentId)
    {
        try {
            $assignment = $teamMember->clickupLists()->findOrFail($assignmentId);
            $assignment->delete();

            return response()->json([
                'success' => true,
                'message' => 'ClickUp list assignment removed successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error removing ClickUp list assignment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get ClickUp folders for a space
     */
    public function getClickupFolders(Request $request)
    {
        $spaceId = $request->get('space_id');

        if (!$spaceId) {
            return response()->json(['success' => false, 'message' => 'Space ID is required']);
        }

        $result = $this->clickUpService->getFolders($spaceId);

        return response()->json($result);
    }

    /**
     * Get ClickUp lists for a folder or space
     */
    public function getClickupLists(Request $request)
    {
        $folderId = $request->get('folder_id');
        $spaceId = $request->get('space_id');

        if (!$folderId && !$spaceId) {
            return response()->json(['success' => false, 'message' => 'Folder ID or Space ID is required']);
        }

        if ($folderId) {
            $result = $this->clickUpService->getLists($folderId);
        } else {
            $result = $this->clickUpService->getListsInSpace($spaceId);
        }

        return response()->json($result);
    }
}
