<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\ProductDocument;
use App\Models\TeamMember;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductDocumentController extends Controller
{
    /**
     * Display a listing of documents for a product
     */
    public function index(Product $product)
    {
        $documents = $product->documents()
            ->with('uploader')
            ->orderBy('title')
            ->orderBy('version_major', 'desc')
            ->orderBy('version_minor', 'desc')
            ->orderBy('version_patch', 'desc')
            ->get()
            ->groupBy('title');

        return view('products.documents.index', compact('product', 'documents'));
    }

    /**
     * Show the form for creating a new document
     */
    public function create(Product $product)
    {
        $teamMembers = TeamMember::where('is_active', true)->get();
        $maxUploadSizeMB = round($this->getMaxUploadSize() / 1024 / 1024, 1);
        return view('products.documents.create', compact('product', 'teamMembers', 'maxUploadSizeMB'));
    }

    /**
     * Store a newly created document
     */
    public function store(Request $request, Product $product)
    {
        try {
            // Debug logging
            \Log::info('Document upload attempt', [
                'product_id' => $product->id,
                'request_data' => $request->except(['file']),
                'has_file' => $request->hasFile('file'),
                'file_info' => $request->hasFile('file') ? [
                    'name' => $request->file('file')->getClientOriginalName(),
                    'size' => $request->file('file')->getSize(),
                    'mime' => $request->file('file')->getMimeType(),
                ] : null
            ]);

            // Get actual server upload limits
            $maxUploadSize = $this->getMaxUploadSize();
            $maxUploadSizeMB = round($maxUploadSize / 1024 / 1024, 1);

            \Log::info('Server upload limits', [
                'max_upload_size_bytes' => $maxUploadSize,
                'max_upload_size_mb' => $maxUploadSizeMB
            ]);

            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string',
                'file' => "required|file|mimes:pdf,doc,docx,txt,md,xlsx,xls,ppt,pptx|max:{$this->getMaxUploadSizeKB()}", // Dynamic max size
                'version_mode' => 'required|in:auto,manual',
                'version_type' => 'required_if:version_mode,auto|in:major,minor,patch',
                'manual_version_number' => 'required_if:version_mode,manual|regex:/^\d+\.\d+\.\d+$/',
                'uploaded_by' => 'nullable|exists:team_members,id',
            ], [
                'file.max' => "The file may not be larger than {$maxUploadSizeMB}MB (server limit).",
                'file.required' => 'Please select a file to upload.',
                'file.mimes' => 'The file must be a PDF, DOC, DOCX, TXT, MD, XLS, XLSX, PPT, or PPTX file.',
            ]);

            // Handle file upload
            $file = $request->file('file');
            if (!$file || !$file->isValid()) {
                throw new \Exception('Invalid file upload');
            }

            $fileName = $file->getClientOriginalName();
            $fileExtension = $file->getClientOriginalExtension();

            // Generate unique file name to avoid conflicts
            $uniqueFileName = Str::slug($validated['title']) . '_' . time() . '.' . $fileExtension;

            // Ensure directory exists
            $directory = 'product-documents/' . $product->id;
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
            }

            // Store file in product-documents directory
            $filePath = $file->storeAs($directory, $uniqueFileName, 'public');

            if (!$filePath) {
                throw new \Exception('Failed to store file');
            }

            // Determine version number
            if ($validated['version_mode'] === 'manual') {
                // Parse manual version
                $versionParts = explode('.', $validated['manual_version_number']);
                $versionInfo = [
                    'version' => $validated['manual_version_number'],
                    'major' => (int)$versionParts[0],
                    'minor' => (int)$versionParts[1],
                    'patch' => (int)$versionParts[2]
                ];

                // Check if this version already exists
                $existingVersion = ProductDocument::where('product_id', $product->id)
                    ->where('title', $validated['title'])
                    ->where('version', $validated['manual_version_number'])
                    ->first();

                if ($existingVersion) {
                    throw new \Exception("Version {$validated['manual_version_number']} already exists for this document");
                }
            } else {
                // Use automatic version calculation
                $versionInfo = $this->calculateVersion($product->id, $validated['title'], $validated['version_type']);
            }

            // Create document record
            $document = ProductDocument::create([
                'product_id' => $product->id,
                'title' => $validated['title'],
                'description' => $validated['description'],
                'file_name' => $fileName,
                'file_path' => $filePath,
                'file_type' => $fileExtension,
                'file_size' => $file->getSize(),
                'version' => $versionInfo['version'],
                'version_major' => $versionInfo['major'],
                'version_minor' => $versionInfo['minor'],
                'version_patch' => $versionInfo['patch'],
                'uploaded_by' => $validated['uploaded_by'],
                'is_current_version' => true,
                'uploaded_at' => now(),
            ]);

            // Mark this as current version and others as old
            $document->markAsCurrent();

            \Log::info('Document uploaded successfully', [
                'document_id' => $document->id,
                'file_path' => $filePath
            ]);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Document uploaded successfully!',
                    'redirect' => route('products.documents.index', $product)
                ]);
            }

            return redirect()->route('products.documents.index', $product)
                ->with('success', 'Document uploaded successfully!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Document upload validation error', [
                'errors' => $e->errors(),
                'product_id' => $product->id
            ]);

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $e->errors()
                ], 422);
            }

            return back()->withErrors($e->errors())->withInput();

        } catch (\Exception $e) {
            \Log::error('Document upload error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'product_id' => $product->id
            ]);

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Upload failed: ' . $e->getMessage()
                ], 500);
            }

            return back()->with('error', 'Upload failed: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Display the specified document
     */
    public function show(Product $product, ProductDocument $document)
    {
        $document->load(['uploader', 'versions']);

        return view('products.documents.show', compact('product', 'document'));
    }

    /**
     * Download the specified document
     */
    public function download(Product $product, ProductDocument $document)
    {
        if (!Storage::disk('public')->exists($document->file_path)) {
            abort(404, 'File not found');
        }

        return Storage::disk('public')->download($document->file_path, $document->file_name);
    }

    /**
     * Show the form for editing the specified document
     */
    public function edit(Product $product, ProductDocument $document)
    {
        $teamMembers = TeamMember::where('is_active', true)->get();
        $maxUploadSizeMB = round($this->getMaxUploadSize() / 1024 / 1024, 1);
        return view('products.documents.edit', compact('product', 'document', 'teamMembers', 'maxUploadSizeMB'));
    }

    /**
     * Update the specified document
     */
    public function update(Request $request, Product $product, ProductDocument $document)
    {
        // Get actual server upload limits
        $maxUploadSize = $this->getMaxUploadSize();
        $maxUploadSizeMB = round($maxUploadSize / 1024 / 1024, 1);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'file' => "nullable|file|mimes:pdf,doc,docx,txt,md,xlsx,xls,ppt,pptx|max:{$this->getMaxUploadSizeKB()}",
            'version_mode' => 'required_with:file|in:auto,manual',
            'version_type' => 'required_if:version_mode,auto|in:major,minor,patch',
            'manual_version_number' => 'required_if:version_mode,manual|regex:/^\d+\.\d+\.\d+$/',
            'uploaded_by' => 'nullable|exists:team_members,id',
        ], [
            'file.max' => "The file may not be larger than {$maxUploadSizeMB}MB (server limit).",
            'file.mimes' => 'The file must be a PDF, DOC, DOCX, TXT, MD, XLS, XLSX, PPT, or PPTX file.',
        ]);

        // If file is uploaded, create new version
        if ($request->hasFile('file')) {
            return $this->createNewVersion($request, $product, $document, $validated);
        }

        // Otherwise, just update metadata
        $document->update([
            'title' => $validated['title'],
            'description' => $validated['description'],
            'uploaded_by' => $validated['uploaded_by'],
        ]);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Document updated successfully!',
                'redirect' => route('products.documents.show', [$product, $document])
            ]);
        }

        return redirect()->route('products.documents.show', [$product, $document])
            ->with('success', 'Document updated successfully!');
    }

    /**
     * Remove the specified document
     */
    public function destroy(Product $product, ProductDocument $document)
    {
        // Delete the physical file
        if (Storage::disk('public')->exists($document->file_path)) {
            Storage::disk('public')->delete($document->file_path);
        }

        $document->delete();

        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Document deleted successfully!'
            ]);
        }

        return redirect()->route('products.documents.index', $product)
            ->with('success', 'Document deleted successfully!');
    }

    /**
     * Create a new version of an existing document
     */
    private function createNewVersion(Request $request, Product $product, ProductDocument $document, array $validated)
    {
        $file = $request->file('file');
        $fileName = $file->getClientOriginalName();
        $fileExtension = $file->getClientOriginalExtension();

        // Generate unique file name
        $uniqueFileName = Str::slug($validated['title']) . '_' . time() . '.' . $fileExtension;

        // Store new file
        $filePath = $file->storeAs('product-documents/' . $product->id, $uniqueFileName, 'public');

        // Calculate new version
        if ($validated['version_mode'] === 'manual') {
            // Parse manual version
            $versionParts = explode('.', $validated['manual_version_number']);
            $versionInfo = [
                'version' => $validated['manual_version_number'],
                'major' => (int)$versionParts[0],
                'minor' => (int)$versionParts[1],
                'patch' => (int)$versionParts[2]
            ];

            // Check if this version already exists
            $existingVersion = ProductDocument::where('product_id', $product->id)
                ->where('title', $validated['title'])
                ->where('version', $validated['manual_version_number'])
                ->first();

            if ($existingVersion) {
                throw new \Exception("Version {$validated['manual_version_number']} already exists for this document");
            }
        } else {
            // Use automatic version calculation
            $versionInfo = $this->calculateVersion($product->id, $validated['title'], $validated['version_type']);
        }

        // Create new document version
        $newDocument = ProductDocument::create([
            'product_id' => $product->id,
            'title' => $validated['title'],
            'description' => $validated['description'],
            'file_name' => $fileName,
            'file_path' => $filePath,
            'file_type' => $fileExtension,
            'file_size' => $file->getSize(),
            'version' => $versionInfo['version'],
            'version_major' => $versionInfo['major'],
            'version_minor' => $versionInfo['minor'],
            'version_patch' => $versionInfo['patch'],
            'uploaded_by' => $validated['uploaded_by'],
            'is_current_version' => true,
            'uploaded_at' => now(),
        ]);

        // Mark this as current version
        $newDocument->markAsCurrent();

        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'New document version created successfully!',
                'redirect' => route('products.documents.show', [$product, $newDocument])
            ]);
        }

        return redirect()->route('products.documents.show', [$product, $newDocument])
            ->with('success', 'New document version created successfully!');
    }

    /**
     * Calculate the next version number based on version type
     */
    private function calculateVersion(int $productId, string $title, string $versionType): array
    {
        $latestDoc = ProductDocument::where('product_id', $productId)
            ->where('title', $title)
            ->orderBy('version_major', 'desc')
            ->orderBy('version_minor', 'desc')
            ->orderBy('version_patch', 'desc')
            ->first();

        if (!$latestDoc) {
            return [
                'version' => '1.0.0',
                'major' => 1,
                'minor' => 0,
                'patch' => 0
            ];
        }

        $major = $latestDoc->version_major;
        $minor = $latestDoc->version_minor;
        $patch = $latestDoc->version_patch;

        switch ($versionType) {
            case 'major':
                $major++;
                $minor = 0;
                $patch = 0;
                break;
            case 'minor':
                $minor++;
                $patch = 0;
                break;
            case 'patch':
                $patch++;
                break;
        }

        return [
            'version' => "{$major}.{$minor}.{$patch}",
            'major' => $major,
            'minor' => $minor,
            'patch' => $patch
        ];
    }

    /**
     * Get version history for a document
     */
    public function versions(Product $product, ProductDocument $document)
    {
        $versions = ProductDocument::where('product_id', $product->id)
            ->where('title', $document->title)
            ->with('uploader')
            ->orderBy('version_major', 'desc')
            ->orderBy('version_minor', 'desc')
            ->orderBy('version_patch', 'desc')
            ->get();

        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'versions' => $versions
            ]);
        }

        return view('products.documents.versions', compact('product', 'document', 'versions'));
    }

    /**
     * Get upload limits for client-side validation
     */
    public function getUploadLimits(Product $product)
    {
        $maxUploadSize = $this->getMaxUploadSize();

        return response()->json([
            'max_upload_size_bytes' => $maxUploadSize,
            'max_upload_size_mb' => round($maxUploadSize / 1024 / 1024, 1),
            'max_upload_size_kb' => $this->getMaxUploadSizeKB(),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
        ]);
    }

    /**
     * Get the maximum upload size in bytes based on server configuration
     */
    private function getMaxUploadSize(): int
    {
        $uploadMax = $this->parseSize(ini_get('upload_max_filesize'));
        $postMax = $this->parseSize(ini_get('post_max_size'));

        // Return the smaller of the two limits
        return min($uploadMax, $postMax);
    }

    /**
     * Get the maximum upload size in KB for Laravel validation
     */
    private function getMaxUploadSizeKB(): int
    {
        return (int) ($this->getMaxUploadSize() / 1024);
    }

    /**
     * Parse size string (e.g., "2M", "1024K") to bytes
     */
    private function parseSize(string $size): int
    {
        $size = trim($size);
        $last = strtolower($size[strlen($size) - 1]);
        $size = (int) $size;

        switch ($last) {
            case 'g':
                $size *= 1024;
            case 'm':
                $size *= 1024;
            case 'k':
                $size *= 1024;
        }

        return $size;
    }
}
