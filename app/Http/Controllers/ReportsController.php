<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\TeamMember;
use App\Models\Kpi;
use App\Models\Evaluation;
use Carbon\Carbon;

class ReportsController extends Controller
{
    public function index()
    {
        // Get date ranges
        $currentMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();
        $currentQuarter = Carbon::now()->startOfQuarter();

        // Product statistics
        $productStats = [
            'total' => Product::count(),
            'in_progress' => Product::where('phase', 'in_progress')->count(),
            'completed_this_month' => Product::where('phase', 'done')
                ->where('completion_date', '>=', $currentMonth)->count(),
            'overdue' => Product::where('phase', '!=', 'done')
                ->where('target_date', '<', now())->count(),
        ];

        // Team performance
        $teamStats = [
            'total_members' => TeamMember::where('is_active', true)->count(),
            'total_kpis' => Kpi::where('is_active', true)->count(),
            'evaluations_this_month' => Evaluation::where('evaluation_date', '>=', $currentMonth)->count(),
        ];

        // Recent evaluations with performance trends
        $recentEvaluations = Evaluation::with(['kpi.teamMember'])
            ->latest('evaluation_date')
            ->take(10)
            ->get();

        // Top performers (based on KPI achievements)
        $topPerformers = TeamMember::with(['kpis.evaluations' => function($query) use ($currentMonth) {
                $query->where('evaluation_date', '>=', $currentMonth);
            }])
            ->where('is_active', true)
            ->get()
            ->map(function($member) {
                $totalEvaluations = $member->kpis->flatMap->evaluations->count();
                $aboveTargetCount = $member->kpis->flatMap->evaluations
                    ->where('status', 'above_target')->count();

                $member->performance_score = $totalEvaluations > 0
                    ? ($aboveTargetCount / $totalEvaluations) * 100
                    : 0;

                return $member;
            })
            ->sortByDesc('performance_score')
            ->take(5);

        // Product progress by phase
        $productsByPhase = Product::selectRaw('phase, COUNT(*) as count')
            ->groupBy('phase')
            ->get()
            ->pluck('count', 'phase');

        return view('reports.index', compact(
            'productStats',
            'teamStats',
            'recentEvaluations',
            'topPerformers',
            'productsByPhase'
        ));
    }

    public function teamPerformance(Request $request)
    {
        $period = $request->get('period', 'monthly');
        $teamMemberId = $request->get('team_member');

        $query = TeamMember::with(['kpis.evaluations']);

        if ($teamMemberId) {
            $query->where('id', $teamMemberId);
        }

        $teamMembers = $query->where('is_active', true)->get();

        return view('reports.team-performance', compact('teamMembers', 'period'));
    }

    public function productProgress(Request $request)
    {
        $phase = $request->get('phase');
        $query = Product::with('teamMembers');

        if ($phase) {
            $query->where('phase', $phase);
        }

        $products = $query->latest()->get();

        return view('reports.product-progress', compact('products', 'phase'));
    }
}
