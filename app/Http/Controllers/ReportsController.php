<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\TeamMember;
use App\Models\Kpi;
use App\Models\Evaluation;
use App\Models\ProductClickupList;
use App\Services\ClickUpService;
use Carbon\Carbon;

class ReportsController extends Controller
{
    protected $clickUpService;

    public function __construct(ClickUpService $clickUpService)
    {
        $this->clickUpService = $clickUpService;
    }

    public function index()
    {
        // Get date ranges
        $currentMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();
        $currentQuarter = Carbon::now()->startOfQuarter();

        // Product statistics
        $productStats = [
            'total' => Product::count(),
            'in_progress' => Product::where('phase', 'in_progress')->count(),
            'completed_this_month' => Product::where('phase', 'done')
                ->where('completion_date', '>=', $currentMonth)->count(),
            'overdue' => Product::where('phase', '!=', 'done')
                ->where('target_date', '<', now())->count(),
        ];

        // Team performance
        $teamStats = [
            'total_members' => TeamMember::where('is_active', true)->count(),
            'total_kpis' => Kpi::where('is_active', true)->count(),
            'evaluations_this_month' => Evaluation::where('evaluation_date', '>=', $currentMonth)->count(),
        ];

        // ClickUp integration statistics
        $clickupStats = [
            'connected_products' => Product::whereHas('clickupLists')->count(),
            'total_assignments' => ProductClickupList::count(),
            'features_lists' => ProductClickupList::where('list_type', 'features')->count(),
            'bugs_lists' => ProductClickupList::where('list_type', 'bugs')->count(),
            'other_lists' => ProductClickupList::where('list_type', 'other')->count(),
        ];

        // Recent evaluations with performance trends
        $recentEvaluations = Evaluation::with(['kpi.teamMember'])
            ->latest('evaluation_date')
            ->take(10)
            ->get();

        // Top performers (based on KPI achievements)
        $topPerformers = TeamMember::with(['kpis.evaluations' => function($query) use ($currentMonth) {
                $query->where('evaluation_date', '>=', $currentMonth);
            }])
            ->where('is_active', true)
            ->get()
            ->map(function($member) {
                $totalEvaluations = $member->kpis->flatMap->evaluations->count();
                $aboveTargetCount = $member->kpis->flatMap->evaluations
                    ->where('status', 'above_target')->count();

                $member->performance_score = $totalEvaluations > 0
                    ? ($aboveTargetCount / $totalEvaluations) * 100
                    : 0;

                return $member;
            })
            ->sortByDesc('performance_score')
            ->take(5);

        // Product progress by phase
        $productsByPhase = Product::selectRaw('phase, COUNT(*) as count')
            ->groupBy('phase')
            ->get()
            ->pluck('count', 'phase');

        return view('reports.index', compact(
            'productStats',
            'teamStats',
            'clickupStats',
            'recentEvaluations',
            'topPerformers',
            'productsByPhase'
        ));
    }

    public function teamPerformance(Request $request)
    {
        $period = $request->get('period', 'monthly');
        $teamMemberId = $request->get('team_member');

        $query = TeamMember::with(['kpis.evaluations']);

        if ($teamMemberId) {
            $query->where('id', $teamMemberId);
        }

        $teamMembers = $query->where('is_active', true)->get();

        return view('reports.team-performance', compact('teamMembers', 'period'));
    }

    public function productProgress(Request $request)
    {
        $phase = $request->get('phase');
        $query = Product::with('teamMembers');

        if ($phase) {
            $query->where('phase', $phase);
        }

        $products = $query->latest()->get();

        return view('reports.product-progress', compact('products', 'phase'));
    }

    /**
     * ClickUp Insights Dashboard
     */
    public function clickupInsights(Request $request)
    {
        // Get all products with ClickUp list assignments
        $productsWithClickup = Product::with(['clickupLists', 'teamMembers'])
            ->whereHas('clickupLists')
            ->get();

        // Get date range for filtering
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));

        // ClickUp statistics
        $clickupStats = [
            'connected_products' => $productsWithClickup->count(),
            'total_assignments' => ProductClickupList::count(),
            'features_lists' => ProductClickupList::where('list_type', 'features')->count(),
            'bugs_lists' => ProductClickupList::where('list_type', 'bugs')->count(),
            'other_lists' => ProductClickupList::where('list_type', 'other')->count(),
        ];

        // Group assignments by type
        $assignmentsByType = ProductClickupList::with(['product'])
            ->get()
            ->groupBy('list_type');

        return view('reports.clickup-insights', compact(
            'productsWithClickup',
            'clickupStats',
            'assignmentsByType',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Get ClickUp progress data for multiple products
     */
    public function getClickupProgress(Request $request)
    {
        $validated = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'product_ids' => 'nullable|array',
            'product_ids.*' => 'exists:products,id',
            'list_type_filter' => 'nullable|in:features,bugs,other',
        ]);

        $startDate = $validated['start_date'] ?? Carbon::now()->subDays(30)->format('Y-m-d');
        $endDate = $validated['end_date'] ?? Carbon::now()->format('Y-m-d');

        // Build query for products
        $query = Product::with(['clickupLists']);

        if (isset($validated['product_ids'])) {
            $query->whereIn('id', $validated['product_ids']);
        } else {
            $query->whereHas('clickupLists');
        }

        $products = $query->get();
        $progressData = [];

        foreach ($products as $product) {
            $productProgress = [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'assignments' => []
            ];

            $assignments = $product->clickupLists;

            // Filter by list type if specified
            if (isset($validated['list_type_filter'])) {
                $assignments = $assignments->where('list_type', $validated['list_type_filter']);
            }

            foreach ($assignments as $assignment) {
                if (!$this->clickUpService->isEnabled()) {
                    continue;
                }

                // Get progress data from ClickUp
                $listProgress = $this->clickUpService->getListTasksProgress(
                    $assignment->clickup_list_id,
                    $startDate,
                    $endDate
                );

                // Build hierarchical path
                $hierarchicalPath = '';
                if (isset($assignment->clickup_list_data['space'])) {
                    $hierarchicalPath .= $assignment->clickup_list_data['space'];

                    if (isset($assignment->clickup_list_data['folder'])) {
                        $hierarchicalPath .= ' > ' . $assignment->clickup_list_data['folder'];
                    } else {
                        $hierarchicalPath .= ' > No Folder';
                    }

                    $hierarchicalPath .= ' > ' . ($assignment->clickup_list_data['name'] ?? 'Unknown List');
                }

                $productProgress['assignments'][] = [
                    'assignment_id' => $assignment->id,
                    'list_id' => $assignment->clickup_list_id,
                    'list_name' => $assignment->clickup_list_data['name'] ?? 'Unknown List',
                    'list_type' => $assignment->list_type,
                    'list_type_name' => $assignment->list_type_name,
                    'hierarchical_path' => $hierarchicalPath,
                    'progress' => $listProgress['success'] ? $listProgress['data'] : null
                ];
            }

            if (!empty($productProgress['assignments'])) {
                $progressData[] = $productProgress;
            }
        }

        return response()->json([
            'success' => true,
            'data' => $progressData,
            'date_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ]);
    }
}
