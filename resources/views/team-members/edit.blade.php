@extends('layouts.app')

@section('page-title', 'Edit Team Member')

@section('content')
<div class="max-w-4xl mx-auto">
    <div class="mb-6">
        <div class="flex items-center space-x-4">
            <a href="{{ route('team-members.index') }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit Team Member</h1>
                <p class="text-gray-600">Update team member information</p>
            </div>
        </div>
    </div>

    <form id="team-member-form" action="{{ route('team-members.update', $teamMember) }}" method="POST" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                    <input type="text" id="name" name="name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('name', $teamMember->name) }}">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                    <input type="email" id="email" name="email" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('email', $teamMember->email) }}">
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                    <input type="text" id="role" name="role" placeholder="e.g., Product Manager, Developer"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('role', $teamMember->role) }}">
                    @error('role')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', $teamMember->is_active) ? 'checked' : '' }}
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                    <label for="is_active" class="ml-2 block text-sm text-gray-900">
                        Active Member
                    </label>
                </div>

                <div class="md:col-span-2">
                    <label for="responsibilities" class="block text-sm font-medium text-gray-700 mb-2">Responsibilities</label>
                    <textarea id="responsibilities" name="responsibilities" rows="4" placeholder="Describe the member's key responsibilities..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">{{ old('responsibilities', $teamMember->responsibilities) }}</textarea>
                    @error('responsibilities')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Product Assignment</h2>
            
            <div id="products-container">
                @if($teamMember->products->count() > 0)
                    @foreach($teamMember->products as $assignedProduct)
                        <div class="product-row grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Product</label>
                                <select name="products[]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                    <option value="">Select Product</option>
                                    @foreach($products as $product)
                                        <option value="{{ $product->id }}" {{ $product->id === $assignedProduct->id ? 'selected' : '' }}>
                                            {{ $product->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Role in Product</label>
                                <div class="flex space-x-2">
                                    <input type="text" name="product_roles[]" placeholder="e.g., Lead Developer, Designer"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                           value="{{ $assignedProduct->pivot->role_in_product }}">
                                    @if(!$loop->first)
                                        <button type="button" class="remove-product text-red-600 hover:text-red-700 px-3 py-2">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="product-row grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Product</label>
                            <select name="products[]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <option value="">Select Product</option>
                                @foreach($products as $product)
                                    <option value="{{ $product->id }}">{{ $product->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role in Product</label>
                            <input type="text" name="product_roles[]" placeholder="e.g., Lead Developer, Designer"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        </div>
                    </div>
                @endif
            </div>
            
            <button type="button" id="add-product" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                <i class="fas fa-plus mr-1"></i> Add Another Product
            </button>
        </div>

        <!-- ClickUp List Assignment Section -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">ClickUp List Assignment</h2>
                    <p class="text-sm text-gray-600">Manage ClickUp lists assigned to this team member</p>
                </div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-external-link-alt text-blue-500"></i>
                    <span class="text-sm text-gray-500">ClickUp Integration</span>
                </div>
            </div>

            @if(!empty($clickupSpaces))
                <div id="clickup-lists-container">
                    @if($teamMember->activeClickupLists->count() > 0)
                        @foreach($teamMember->activeClickupLists as $assignment)
                            <div class="clickup-list-row border border-gray-200 rounded-lg p-4 mb-4 bg-gray-50" data-assignment-id="{{ $assignment->id }}">
                                <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
                                    <!-- Space Selection -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            <i class="fas fa-layer-group mr-1 text-blue-500"></i>
                                            ClickUp Space
                                        </label>
                                        <select name="clickup_spaces[]" class="clickup-space-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                            <option value="">Select Space</option>
                                            @foreach($clickupSpaces as $space)
                                                <option value="{{ $space['id'] }}"
                                                    {{ ($assignment->clickup_space_data['id'] ?? '') == $space['id'] ? 'selected' : '' }}>
                                                    {{ $space['name'] }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>

                                    <!-- Folder Selection -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            <i class="fas fa-folder mr-1 text-yellow-500"></i>
                                            ClickUp Folder
                                        </label>
                                        <select name="clickup_folders[]" class="clickup-folder-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                            <option value="">Select Folder</option>
                                            @if($assignment->clickup_folder_data)
                                                <option value="{{ $assignment->clickup_folder_data['id'] }}" selected>
                                                    {{ $assignment->clickup_folder_data['name'] }}
                                                </option>
                                            @else
                                                <option value="no-folder" selected>No Folder (Direct Lists)</option>
                                            @endif
                                        </select>
                                    </div>

                                    <!-- List Selection -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            <i class="fas fa-list mr-1 text-green-500"></i>
                                            ClickUp List
                                        </label>
                                        <select name="clickup_lists[]" class="clickup-list-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                            <option value="">Select List</option>
                                            <option value="{{ $assignment->clickup_list_id }}" selected>
                                                {{ $assignment->clickup_list_data['name'] ?? 'Unknown List' }}
                                            </option>
                                        </select>
                                    </div>

                                    <!-- List Type -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            <i class="fas fa-tag mr-1 text-purple-500"></i>
                                            List Type
                                        </label>
                                        <select name="clickup_list_types[]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                            <option value="features" {{ $assignment->list_type == 'features' ? 'selected' : '' }}>Features</option>
                                            <option value="bugs" {{ $assignment->list_type == 'bugs' ? 'selected' : '' }}>Bugs</option>
                                            <option value="other" {{ $assignment->list_type == 'other' ? 'selected' : '' }}>Other</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Notes Section -->
                                <div class="mt-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-sticky-note mr-1 text-gray-500"></i>
                                        Notes (Optional)
                                    </label>
                                    <input type="text" name="clickup_notes[]" value="{{ $assignment->notes }}" placeholder="Add notes about this assignment..."
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                @if(!$loop->first)
                                    <div class="flex justify-end mt-4">
                                        <button type="button" class="remove-clickup-list text-red-600 hover:text-red-700 px-3 py-2 text-sm font-medium">
                                            <i class="fas fa-trash mr-1"></i> Remove
                                        </button>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    @else
                        <div class="clickup-list-row border border-gray-200 rounded-lg p-4 mb-4 bg-gray-50">
                            <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
                                <!-- Space Selection -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-layer-group mr-1 text-blue-500"></i>
                                        ClickUp Space
                                    </label>
                                    <select name="clickup_spaces[]" class="clickup-space-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="">Select Space</option>
                                        @foreach($clickupSpaces as $space)
                                            <option value="{{ $space['id'] }}">{{ $space['name'] }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Folder Selection -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-folder mr-1 text-yellow-500"></i>
                                        ClickUp Folder
                                    </label>
                                    <select name="clickup_folders[]" class="clickup-folder-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" disabled>
                                        <option value="">Select Folder</option>
                                    </select>
                                </div>

                                <!-- List Selection -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-list mr-1 text-green-500"></i>
                                        ClickUp List
                                    </label>
                                    <select name="clickup_lists[]" class="clickup-list-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" disabled>
                                        <option value="">Select List</option>
                                    </select>
                                </div>

                                <!-- List Type -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-tag mr-1 text-purple-500"></i>
                                        List Type
                                    </label>
                                    <select name="clickup_list_types[]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="features">Features</option>
                                        <option value="bugs">Bugs</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Notes Section -->
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-sticky-note mr-1 text-gray-500"></i>
                                    Notes (Optional)
                                </label>
                                <input type="text" name="clickup_notes[]" placeholder="Add notes about this assignment..."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                    @endif
                </div>

                <button type="button" id="add-clickup-list" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                    <i class="fas fa-plus mr-1"></i> Add Another ClickUp List
                </button>
            @else
                <div class="text-center py-8">
                    <div class="text-gray-400 mb-4">
                        <i class="fas fa-exclamation-triangle text-4xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">ClickUp Integration Not Available</h3>
                    <p class="text-gray-600">ClickUp integration is not configured or enabled. Please check your ClickUp settings.</p>
                </div>
            @endif
        </div>

        <div class="flex justify-end space-x-4">
            <a href="{{ route('team-members.index') }}" 
               class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                Cancel
            </a>
            <button type="submit" 
                    class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 font-medium">
                <i class="fas fa-save mr-2"></i>
                Update Team Member
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Add product functionality
    $('#add-product').click(function() {
        const container = $('#products-container');
        const newRow = $('.product-row:first').clone();
        
        // Clear values
        newRow.find('select').val('');
        newRow.find('input').val('');
        
        // Add remove button if not exists
        if (newRow.find('.remove-product').length === 0) {
            const roleDiv = newRow.find('input[name="product_roles[]"]').parent();
            roleDiv.removeClass('flex space-x-2');
            roleDiv.addClass('flex space-x-2');
            newRow.find('input[name="product_roles[]"]').addClass('flex-1');
            roleDiv.append(`
                <button type="button" class="remove-product text-red-600 hover:text-red-700 px-3 py-2">
                    <i class="fas fa-trash"></i>
                </button>
            `);
        }
        
        container.append(newRow);
    });

    // Remove product functionality
    $(document).on('click', '.remove-product', function() {
        $(this).closest('.product-row').remove();
    });

    // Add ClickUp list functionality
    $('#add-clickup-list').click(function() {
        const container = $('#clickup-lists-container');
        const newRow = $('.clickup-list-row:first').clone();

        // Clear values
        newRow.find('select').val('');
        newRow.find('input').val('');
        newRow.removeAttr('data-assignment-id');

        // Reset disabled states
        newRow.find('.clickup-folder-select').prop('disabled', true);
        newRow.find('.clickup-list-select').prop('disabled', true);

        // Add remove button
        newRow.append(`
            <div class="flex justify-end mt-4">
                <button type="button" class="remove-clickup-list text-red-600 hover:text-red-700 px-3 py-2 text-sm font-medium">
                    <i class="fas fa-trash mr-1"></i> Remove
                </button>
            </div>
        `);

        container.append(newRow);
    });

    // Remove ClickUp list functionality
    $(document).on('click', '.remove-clickup-list', function() {
        $(this).closest('.clickup-list-row').remove();
    });

    // ClickUp Space selection change
    $(document).on('change', '.clickup-space-select', function() {
        const spaceId = $(this).val();
        const row = $(this).closest('.clickup-list-row');
        const folderSelect = row.find('.clickup-folder-select');
        const listSelect = row.find('.clickup-list-select');

        // Reset dependent dropdowns
        folderSelect.html('<option value="">Select Folder</option>').prop('disabled', true);
        listSelect.html('<option value="">Select List</option>').prop('disabled', true);

        if (spaceId) {
            // Load folders for this space
            $.ajax({
                url: '{{ route("team-members.clickup.folders") }}',
                type: 'GET',
                data: { space_id: spaceId },
                success: function(response) {
                    if (response.success && response.folders) {
                        folderSelect.prop('disabled', false);

                        // Add folders to dropdown
                        response.folders.forEach(function(folder) {
                            folderSelect.append(`<option value="${folder.id}">${folder.name}</option>`);
                        });

                        // Add "No Folder" option for lists directly under space
                        folderSelect.append('<option value="no-folder">No Folder (Direct Lists)</option>');
                    }
                },
                error: function(xhr) {
                    console.error('Error loading folders:', xhr.responseJSON);
                }
            });
        }
    });

    // ClickUp Folder selection change
    $(document).on('change', '.clickup-folder-select', function() {
        const folderId = $(this).val();
        const row = $(this).closest('.clickup-list-row');
        const listSelect = row.find('.clickup-list-select');
        const spaceSelect = row.find('.clickup-space-select');
        const spaceId = spaceSelect.val();

        // Reset list dropdown
        listSelect.html('<option value="">Select List</option>').prop('disabled', true);

        if (folderId && spaceId) {
            let url = '{{ route("team-members.clickup.lists") }}';
            let data = {};

            if (folderId === 'no-folder') {
                // Load lists directly under space
                data.space_id = spaceId;
            } else {
                // Load lists under folder
                data.folder_id = folderId;
            }

            $.ajax({
                url: url,
                type: 'GET',
                data: data,
                success: function(response) {
                    if (response.success && response.lists) {
                        listSelect.prop('disabled', false);

                        response.lists.forEach(function(list) {
                            listSelect.append(`<option value="${list.id}">${list.name}</option>`);
                        });
                    }
                },
                error: function(xhr) {
                    console.error('Error loading lists:', xhr.responseJSON);
                }
            });
        }
    });

    // Form submission with AJAX
    $('#team-member-form').submit(function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        // Debug: Log form data
        console.log('Team member edit form data being sent:');
        for (let [key, value] of formData.entries()) {
            console.log(key, value);
        }

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('Success response:', response);
                if (response.success) {
                    window.location.href = response.redirect;
                }
            },
            error: function(xhr) {
                console.log('Error response:', xhr.responseJSON);
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    // Clear previous errors
                    $('.text-red-600').remove();

                    // Display new errors
                    Object.keys(errors).forEach(function(key) {
                        const input = $(`[name="${key}"]`);
                        input.after(`<p class="mt-1 text-sm text-red-600">${errors[key][0]}</p>`);
                    });
                } else {
                    alert('Error updating team member: ' + (xhr.responseJSON?.message || 'Unknown error'));
                }
            }
        });
    });
});
</script>
@endpush
@endsection
