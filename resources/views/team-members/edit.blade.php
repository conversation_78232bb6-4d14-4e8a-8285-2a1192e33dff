@extends('layouts.app')

@section('page-title', 'Edit Team Member')

@section('content')
<div class="max-w-4xl mx-auto">
    <div class="mb-6">
        <div class="flex items-center space-x-4">
            <a href="{{ route('team-members.index') }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit Team Member</h1>
                <p class="text-gray-600">Update team member information</p>
            </div>
        </div>
    </div>

    <form id="team-member-form" action="{{ route('team-members.update', $teamMember) }}" method="POST" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                    <input type="text" id="name" name="name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('name', $teamMember->name) }}">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                    <input type="email" id="email" name="email" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('email', $teamMember->email) }}">
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                    <input type="text" id="role" name="role" placeholder="e.g., Product Manager, Developer"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('role', $teamMember->role) }}">
                    @error('role')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', $teamMember->is_active) ? 'checked' : '' }}
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                    <label for="is_active" class="ml-2 block text-sm text-gray-900">
                        Active Member
                    </label>
                </div>

                <div class="md:col-span-2">
                    <label for="responsibilities" class="block text-sm font-medium text-gray-700 mb-2">Responsibilities</label>
                    <textarea id="responsibilities" name="responsibilities" rows="4" placeholder="Describe the member's key responsibilities..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">{{ old('responsibilities', $teamMember->responsibilities) }}</textarea>
                    @error('responsibilities')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Product Assignment</h2>
            
            <div id="products-container">
                @if($teamMember->products->count() > 0)
                    @foreach($teamMember->products as $assignedProduct)
                        <div class="product-row grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Product</label>
                                <select name="products[]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                    <option value="">Select Product</option>
                                    @foreach($products as $product)
                                        <option value="{{ $product->id }}" {{ $product->id === $assignedProduct->id ? 'selected' : '' }}>
                                            {{ $product->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Role in Product</label>
                                <div class="flex space-x-2">
                                    <input type="text" name="product_roles[]" placeholder="e.g., Lead Developer, Designer"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                           value="{{ $assignedProduct->pivot->role_in_product }}">
                                    @if(!$loop->first)
                                        <button type="button" class="remove-product text-red-600 hover:text-red-700 px-3 py-2">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="product-row grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Product</label>
                            <select name="products[]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <option value="">Select Product</option>
                                @foreach($products as $product)
                                    <option value="{{ $product->id }}">{{ $product->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role in Product</label>
                            <input type="text" name="product_roles[]" placeholder="e.g., Lead Developer, Designer"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        </div>
                    </div>
                @endif
            </div>
            
            <button type="button" id="add-product" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                <i class="fas fa-plus mr-1"></i> Add Another Product
            </button>
        </div>

        <div class="flex justify-end space-x-4">
            <a href="{{ route('team-members.index') }}" 
               class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                Cancel
            </a>
            <button type="submit" 
                    class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 font-medium">
                <i class="fas fa-save mr-2"></i>
                Update Team Member
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Add product functionality
    $('#add-product').click(function() {
        const container = $('#products-container');
        const newRow = $('.product-row:first').clone();
        
        // Clear values
        newRow.find('select').val('');
        newRow.find('input').val('');
        
        // Add remove button if not exists
        if (newRow.find('.remove-product').length === 0) {
            const roleDiv = newRow.find('input[name="product_roles[]"]').parent();
            roleDiv.removeClass('flex space-x-2');
            roleDiv.addClass('flex space-x-2');
            newRow.find('input[name="product_roles[]"]').addClass('flex-1');
            roleDiv.append(`
                <button type="button" class="remove-product text-red-600 hover:text-red-700 px-3 py-2">
                    <i class="fas fa-trash"></i>
                </button>
            `);
        }
        
        container.append(newRow);
    });

    // Remove product functionality
    $(document).on('click', '.remove-product', function() {
        $(this).closest('.product-row').remove();
    });

    // Form submission with AJAX
    $('#team-member-form').submit(function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        // Debug: Log form data
        console.log('Team member edit form data being sent:');
        for (let [key, value] of formData.entries()) {
            console.log(key, value);
        }

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('Success response:', response);
                if (response.success) {
                    window.location.href = response.redirect;
                }
            },
            error: function(xhr) {
                console.log('Error response:', xhr.responseJSON);
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    // Clear previous errors
                    $('.text-red-600').remove();

                    // Display new errors
                    Object.keys(errors).forEach(function(key) {
                        const input = $(`[name="${key}"]`);
                        input.after(`<p class="mt-1 text-sm text-red-600">${errors[key][0]}</p>`);
                    });
                } else {
                    alert('Error updating team member: ' + (xhr.responseJSON?.message || 'Unknown error'));
                }
            }
        });
    });
});
</script>
@endpush
@endsection
