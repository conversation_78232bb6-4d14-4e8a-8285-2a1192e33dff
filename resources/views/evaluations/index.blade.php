@extends('layouts.app')

@section('page-title', 'Evaluations')

@section('content')
<div class="space-y-6">
    <!-- Header with Actions -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">KPI Evaluations</h1>
            <p class="text-gray-600">Track and review performance evaluations</p>
        </div>
        <a href="{{ route('evaluations.create') }}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i class="fas fa-plus mr-2"></i>
            Add Evaluation
        </a>
    </div>

    <!-- Evaluations Table -->
    <div class="bg-white rounded-lg shadow">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KPI</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Team Member</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($evaluations as $evaluation)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">{{ $evaluation->kpi->name }}</div>
                                <div class="text-sm text-gray-500">Target: {{ $evaluation->kpi->target_value }} {{ $evaluation->kpi->metric_type }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">
                                        {{ substr($evaluation->kpi->teamMember->name, 0, 1) }}
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $evaluation->kpi->teamMember->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $evaluation->kpi->teamMember->role }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ $evaluation->actual_value }} / {{ $evaluation->kpi->target_value }}
                                </div>
                                <div class="text-sm text-gray-500">
                                    {{ number_format($evaluation->performance_percentage, 1) }}% of target
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                {{ $evaluation->evaluation_date->format('M d, Y') }}
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $evaluation->status_badge_color }}">
                                    {{ ucfirst(str_replace('_', ' ', $evaluation->status)) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="showEvaluation({{ $evaluation->id }})" 
                                            class="text-primary-600 hover:text-primary-900" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <a href="{{ route('evaluations.destroy', $evaluation) }}" 
                                       class="text-red-600 hover:text-red-900 delete-evaluation" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                                <i class="fas fa-clipboard-check text-4xl mb-4 text-gray-300"></i>
                                <div class="text-lg font-medium">No evaluations found</div>
                                <div class="text-sm">Start tracking performance by adding evaluations</div>
                                <a href="{{ route('evaluations.create') }}" 
                                   class="mt-4 inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
                                    <i class="fas fa-plus mr-2"></i>
                                    Add Evaluation
                                </a>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($evaluations->hasPages())
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $evaluations->links() }}
            </div>
        @endif
    </div>
</div>

<!-- Evaluation Details Modal -->
<div id="evaluation-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Evaluation Details</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="evaluation-content">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function showEvaluation(id) {
    // For now, just show a simple alert. In a full implementation, 
    // this would load evaluation details via AJAX
    const evaluation = @json($evaluations->keyBy('id'));
    const eval = evaluation[id];
    
    if (eval) {
        let content = `
            <div class="space-y-4">
                <div>
                    <h4 class="font-medium text-gray-900">${eval.kpi.name}</h4>
                    <p class="text-sm text-gray-600">${eval.kpi.team_member.name}</p>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm text-gray-600">Actual Value:</span>
                        <span class="font-medium">${eval.actual_value}</span>
                    </div>
                    <div>
                        <span class="text-sm text-gray-600">Target Value:</span>
                        <span class="font-medium">${eval.kpi.target_value}</span>
                    </div>
                </div>
                ${eval.comments ? `<div><span class="text-sm text-gray-600">Comments:</span><p class="mt-1">${eval.comments}</p></div>` : ''}
                ${eval.recommendations ? `<div><span class="text-sm text-gray-600">Recommendations:</span><p class="mt-1">${eval.recommendations}</p></div>` : ''}
            </div>
        `;
        
        document.getElementById('evaluation-content').innerHTML = content;
        document.getElementById('evaluation-modal').classList.remove('hidden');
    }
}

function closeModal() {
    document.getElementById('evaluation-modal').classList.add('hidden');
}

// Delete evaluation
$(document).on('click', '.delete-evaluation', function(e) {
    e.preventDefault();
    
    if (!confirm('Are you sure you want to delete this evaluation?')) {
        return;
    }

    const url = $(this).attr('href');
    
    $.ajax({
        url: url,
        type: 'DELETE',
        success: function(response) {
            if (response.success) {
                location.reload();
            }
        },
        error: function() {
            alert('Error deleting evaluation');
        }
    });
});
</script>
@endpush
@endsection
