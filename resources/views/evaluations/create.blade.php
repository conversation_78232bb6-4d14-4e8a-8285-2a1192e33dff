@extends('layouts.app')

@section('page-title', 'Add Evaluation')

@section('content')
<div class="max-w-4xl mx-auto">
    <div class="mb-6">
        <div class="flex items-center space-x-4">
            <a href="{{ route('evaluations.index') }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Add KPI Evaluation</h1>
                <p class="text-gray-600">Record performance data for a KPI</p>
            </div>
        </div>
    </div>

    <form action="{{ route('evaluations.store') }}" method="POST" class="space-y-6">
        @csrf
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Evaluation Details</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="kpi_id" class="block text-sm font-medium text-gray-700 mb-2">KPI *</label>
                    <select id="kpi_id" name="kpi_id" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">Select KPI</option>
                        @foreach($kpis as $kpi)
                            <option value="{{ $kpi->id }}" {{ old('kpi_id', $selectedKpi) == $kpi->id ? 'selected' : '' }}>
                                {{ $kpi->name }} - {{ $kpi->teamMember->name }} (Target: {{ $kpi->target_value }})
                            </option>
                        @endforeach
                    </select>
                    @error('kpi_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="evaluation_date" class="block text-sm font-medium text-gray-700 mb-2">Evaluation Date *</label>
                    <input type="date" id="evaluation_date" name="evaluation_date" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('evaluation_date', date('Y-m-d')) }}">
                    @error('evaluation_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="actual_value" class="block text-sm font-medium text-gray-700 mb-2">Actual Value *</label>
                    <input type="number" id="actual_value" name="actual_value" required step="0.01" min="0"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('actual_value') }}">
                    @error('actual_value')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="md:col-span-2">
                    <label for="comments" class="block text-sm font-medium text-gray-700 mb-2">Comments</label>
                    <textarea id="comments" name="comments" rows="3" placeholder="Add any observations or notes..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">{{ old('comments') }}</textarea>
                    @error('comments')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="md:col-span-2">
                    <label for="recommendations" class="block text-sm font-medium text-gray-700 mb-2">Recommendations</label>
                    <textarea id="recommendations" name="recommendations" rows="3" placeholder="Suggestions for improvement or next steps..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">{{ old('recommendations') }}</textarea>
                    @error('recommendations')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Evaluation Guidelines</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Enter the actual measured value for the selected KPI</li>
                            <li>The system will automatically determine if performance is below, on, or above target</li>
                            <li>Add comments to provide context about the performance</li>
                            <li>Include recommendations for improvement or maintaining performance</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex justify-end space-x-4">
            <a href="{{ route('evaluations.index') }}" 
               class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                Cancel
            </a>
            <button type="submit" 
                    class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 font-medium">
                <i class="fas fa-save mr-2"></i>
                Save Evaluation
            </button>
        </div>
    </form>
</div>
@endsection
