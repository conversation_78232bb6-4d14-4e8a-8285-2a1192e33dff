@extends('layouts.app')

@section('page-title', 'Reports')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div>
        <h1 class="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
        <p class="text-gray-600">Track performance, progress, and team insights</p>
    </div>

    <!-- Overview Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-box text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $productStats['total'] }}</h3>
                    <p class="text-sm text-gray-600">Total Products</p>
                    <p class="text-xs text-blue-600">{{ $productStats['in_progress'] }} in progress</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $productStats['completed_this_month'] }}</h3>
                    <p class="text-sm text-gray-600">Completed This Month</p>
                    <p class="text-xs text-green-600">Products delivered</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $teamStats['total_members'] }}</h3>
                    <p class="text-sm text-gray-600">Active Team Members</p>
                    <p class="text-xs text-yellow-600">{{ $teamStats['total_kpis'] }} active KPIs</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-chart-line text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $teamStats['evaluations_this_month'] }}</h3>
                    <p class="text-sm text-gray-600">Evaluations This Month</p>
                    <p class="text-xs text-purple-600">Performance tracking</p>
                </div>
            </div>
        </div>
    </div>

    <!-- ClickUp Integration Overview -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center text-white">
                        <i class="fas fa-link text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">ClickUp Integration</h3>
                        <p class="text-sm text-gray-600">Progress insights and reporting across all connected products</p>
                    </div>
                </div>
                <a href="{{ route('reports.clickup-insights') }}"
                   class="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm rounded-lg hover:bg-purple-700 transition-colors">
                    <i class="fas fa-chart-line mr-2"></i>
                    View Detailed Insights
                </a>
            </div>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-box text-blue-600"></i>
                    </div>
                    <div class="text-2xl font-bold text-gray-900">{{ $clickupStats['connected_products'] }}</div>
                    <div class="text-sm text-gray-600">Connected Products</div>
                </div>

                <div class="text-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-list text-purple-600"></i>
                    </div>
                    <div class="text-2xl font-bold text-gray-900">{{ $clickupStats['total_assignments'] }}</div>
                    <div class="text-sm text-gray-600">List Assignments</div>
                </div>

                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-star text-green-600"></i>
                    </div>
                    <div class="text-2xl font-bold text-gray-900">{{ $clickupStats['features_lists'] }}</div>
                    <div class="text-sm text-gray-600">Features Lists</div>
                </div>

                <div class="text-center">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-bug text-red-600"></i>
                    </div>
                    <div class="text-2xl font-bold text-gray-900">{{ $clickupStats['bugs_lists'] }}</div>
                    <div class="text-sm text-gray-600">Bug Lists</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Product Progress Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Product Progress</h2>
            <div class="space-y-4">
                @php
                    $total = $productsByPhase->sum();
                @endphp
                @foreach(['idea' => 'Ideas', 'in_progress' => 'In Progress', 'done' => 'Completed'] as $phase => $label)
                    @php
                        $count = $productsByPhase->get($phase, 0);
                        $percentage = $total > 0 ? ($count / $total) * 100 : 0;
                        $colorClass = match($phase) {
                            'idea' => 'bg-gray-200',
                            'in_progress' => 'bg-blue-500',
                            'done' => 'bg-green-500',
                            default => 'bg-gray-200'
                        };
                    @endphp
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span class="font-medium text-gray-700">{{ $label }}</span>
                            <span class="text-gray-600">{{ $count }} ({{ number_format($percentage, 1) }}%)</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="{{ $colorClass }} h-2 rounded-full" style="width: {{ $percentage }}%"></div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Top Performers -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Top Performers This Month</h2>
            @if($topPerformers->count() > 0)
                <div class="space-y-3">
                    @foreach($topPerformers as $performer)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">
                                    {{ substr($performer->name, 0, 1) }}
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">{{ $performer->name }}</div>
                                    <div class="text-sm text-gray-600">{{ $performer->role }}</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="font-medium text-green-600">{{ number_format($performer->performance_score, 1) }}%</div>
                                <div class="text-xs text-gray-500">Performance</div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-gray-500 text-center py-4">No performance data available</p>
            @endif
        </div>
    </div>

    <!-- Recent Evaluations -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Recent Evaluations</h2>
        </div>
        <div class="p-6">
            @if($recentEvaluations->count() > 0)
                <div class="space-y-4">
                    @foreach($recentEvaluations as $evaluation)
                        <div class="flex items-center justify-between border-b border-gray-100 pb-3">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium">
                                        {{ substr($evaluation->kpi->teamMember->name, 0, 1) }}
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900">{{ $evaluation->kpi->name }}</div>
                                        <div class="text-sm text-gray-600">{{ $evaluation->kpi->teamMember->name }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="font-medium text-gray-900">
                                    {{ $evaluation->actual_value }} / {{ $evaluation->kpi->target_value }}
                                </div>
                                <div class="text-xs text-gray-500">{{ $evaluation->evaluation_date->format('M d, Y') }}</div>
                            </div>
                            <div class="text-right">
                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $evaluation->status_badge_color }}">
                                    {{ ucfirst(str_replace('_', ' ', $evaluation->status)) }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-gray-500 text-center py-8">No evaluations found</p>
            @endif
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Detailed Reports</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{{ route('reports.team-performance') }}" 
               class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-users text-blue-600 text-xl mr-3"></i>
                <div>
                    <div class="font-medium text-gray-900">Team Performance</div>
                    <div class="text-sm text-gray-600">Detailed KPI analysis</div>
                </div>
            </a>
            
            <a href="{{ route('reports.product-progress') }}" 
               class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-chart-pie text-green-600 text-xl mr-3"></i>
                <div>
                    <div class="font-medium text-gray-900">Product Progress</div>
                    <div class="text-sm text-gray-600">Timeline and completion</div>
                </div>
            </a>
            
            <a href="{{ route('kpis.index') }}" 
               class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-chart-bar text-purple-600 text-xl mr-3"></i>
                <div>
                    <div class="font-medium text-gray-900">KPI Management</div>
                    <div class="text-sm text-gray-600">Manage and track KPIs</div>
                </div>
            </a>
        </div>
    </div>
</div>
@endsection
