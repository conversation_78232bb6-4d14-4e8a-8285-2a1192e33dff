@extends('layouts.app')

@section('page-title', 'ClickUp Insights & Progress Reports')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
    <!-- Enhanced Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-xl flex items-center justify-center text-white shadow-lg">
                        <i class="fas fa-chart-line text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">ClickUp Insights</h1>
                        <p class="text-sm text-gray-600 mt-1">Comprehensive progress reports and analytics across all products</p>
                    </div>
                </div>
                
                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                    <button onclick="refreshAllData()"
                            class="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-sm rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105">
                        <i class="fas fa-sync mr-2"></i>
                        <span class="hidden sm:inline">Refresh All Data</span>
                        <span class="sm:hidden">Refresh</span>
                    </button>
                    <button onclick="openEmailModal()"
                            class="px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white text-sm rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105">
                        <i class="fas fa-envelope mr-2"></i>
                        <span class="hidden sm:inline">Email Report</span>
                        <span class="sm:hidden">Email</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- ClickUp Integration Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Connected Products</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $clickupStats['connected_products'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-link text-blue-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total List Assignments</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $clickupStats['total_assignments'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-list text-purple-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Features Lists</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $clickupStats['features_lists'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-star text-green-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Bug Lists</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $clickupStats['bugs_lists'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-bug text-red-600"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Date Range Filter -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center text-white">
                        <i class="fas fa-calendar text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Date Range Filter</h3>
                        <p class="text-sm text-gray-600">Select date range for progress analysis</p>
                    </div>
                </div>
                
                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                    <div class="flex flex-col">
                        <label class="text-xs font-medium text-gray-700 mb-1">Start Date</label>
                        <input type="date" id="startDate" value="{{ $startDate }}"
                               class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex flex-col">
                        <label class="text-xs font-medium text-gray-700 mb-1">End Date</label>
                        <input type="date" id="endDate" value="{{ $endDate }}"
                               class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex items-end">
                        <button onclick="updateDateRange()"
                                class="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-search mr-2"></i>
                            Update
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Data Container -->
        <div id="progressContainer" class="space-y-6">
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-chart-line text-2xl text-blue-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Loading Progress Data...</h3>
                <p class="text-gray-600">Please wait while we fetch the latest ClickUp progress information.</p>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Load initial progress data
document.addEventListener('DOMContentLoaded', function() {
    loadProgressData();
});

function loadProgressData() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const container = document.getElementById('progressContainer');
    
    // Show loading state
    container.innerHTML = `
        <div class="text-center py-12">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <i class="fas fa-spinner fa-spin text-2xl text-blue-600"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Loading Progress Data...</h3>
            <p class="text-gray-600">Fetching latest ClickUp progress information...</p>
        </div>
    `;
    
    // Fetch progress data
    fetch(`{{ route('reports.clickup-progress') }}?start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProgressData(data.data, data.date_range);
            } else {
                container.innerHTML = `
                    <div class="text-center py-12 text-red-600">
                        <i class="fas fa-exclamation-triangle text-3xl mb-3"></i>
                        <p>Error loading progress data: ${data.message}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            container.innerHTML = `
                <div class="text-center py-12 text-red-600">
                    <i class="fas fa-exclamation-triangle text-3xl mb-3"></i>
                    <p>Error loading progress data. Please try again.</p>
                </div>
            `;
        });
}

function displayProgressData(progressData, dateRange) {
    const container = document.getElementById('progressContainer');
    
    if (!progressData || progressData.length === 0) {
        container.innerHTML = `
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-chart-line text-2xl text-gray-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Progress Data Available</h3>
                <p class="text-gray-600">No ClickUp progress data found for the selected date range.</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    
    progressData.forEach(product => {
        html += `
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-lg flex items-center justify-center text-white">
                                <i class="fas fa-box text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900">${product.product_name}</h3>
                                <p class="text-sm text-gray-600">${product.assignments.length} ClickUp list${product.assignments.length !== 1 ? 's' : ''} assigned</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Product ID: ${product.product_id}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="space-y-6">
        `;
        
        product.assignments.forEach(assignment => {
            const progress = assignment.progress;
            const listTypeColors = {
                'features': 'blue',
                'bugs': 'red',
                'other': 'gray'
            };
            const color = listTypeColors[assignment.list_type] || 'gray';
            
            html += `
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${color}-100 text-${color}-800">
                                ${assignment.list_type_name}
                            </span>
                            <h4 class="font-semibold text-gray-900">${assignment.list_name}</h4>
                        </div>
                    </div>
                    
                    ${assignment.hierarchical_path ? `
                        <div class="mb-4">
                            <p class="text-sm text-gray-600">
                                <i class="fas fa-sitemap mr-2"></i>
                                ${assignment.hierarchical_path}
                            </p>
                        </div>
                    ` : ''}
                    
                    ${progress ? `
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-900">${progress.total_tasks}</div>
                                <div class="text-sm text-gray-600">Total Tasks</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">${progress.completed_tasks}</div>
                                <div class="text-sm text-gray-600">Completed</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">${progress.in_progress_tasks}</div>
                                <div class="text-sm text-gray-600">In Progress</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-orange-600">${progress.completion_rate}%</div>
                                <div class="text-sm text-gray-600">Completion Rate</div>
                            </div>
                        </div>
                    ` : `
                        <div class="text-center py-4 text-gray-500">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            No progress data available
                        </div>
                    `}
                </div>
            `;
        });
        
        html += `
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

function updateDateRange() {
    loadProgressData();
}

function refreshAllData() {
    loadProgressData();
}

function openEmailModal() {
    // TODO: Implement email modal for centralized reporting
    alert('Email reporting functionality will be implemented in the next phase.');
}
</script>
@endpush
@endsection
