@extends('layouts.app')

@section('page-title', 'Team Member ClickUp Insights')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
    <!-- Enhanced Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-600 to-indigo-700 rounded-xl flex items-center justify-center text-white shadow-lg">
                        <i class="fas fa-users-cog text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Team Member ClickUp Insights</h1>
                        <p class="text-gray-600 mt-1">Track team member ClickUp assignments and progress</p>
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
                    <a href="{{ route('reports.index') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Reports
                    </a>
                    <button onclick="openEmailModal()"
                            class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white text-sm rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105">
                        <i class="fas fa-envelope mr-2"></i>
                        Email Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Team Member ClickUp Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Connected Members</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $teamClickupStats['connected_team_members'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-check text-purple-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Assignments</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $teamClickupStats['total_assignments'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-tasks text-blue-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Features Lists</p>
                        <p class="text-2xl font-bold text-green-600">{{ $teamClickupStats['features_lists'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-star text-green-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Bug Lists</p>
                        <p class="text-2xl font-bold text-red-600">{{ $teamClickupStats['bugs_lists'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-bug text-red-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Other Lists</p>
                        <p class="text-2xl font-bold text-gray-600">{{ $teamClickupStats['other_lists'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-list text-gray-600"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Team Member Assignments Overview -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Team Member Assignments Overview</h2>
                <p class="text-sm text-gray-600 mt-1">ClickUp list assignments by team member</p>
            </div>
            <div class="p-6">
                @if($teamMemberStats->count() > 0)
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        @foreach($teamMemberStats as $memberStat)
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow duration-200">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                                            {{ substr($memberStat['member']->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <h3 class="font-medium text-gray-900">{{ $memberStat['member']->name }}</h3>
                                            <p class="text-sm text-gray-600">{{ $memberStat['member']->position }}</p>
                                        </div>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $memberStat['total_assignments'] }} assignments
                                    </span>
                                </div>
                                
                                <div class="grid grid-cols-3 gap-3">
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-green-600">{{ $memberStat['features_count'] }}</div>
                                        <div class="text-xs text-gray-600">Features</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-red-600">{{ $memberStat['bugs_count'] }}</div>
                                        <div class="text-xs text-gray-600">Bugs</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-gray-600">{{ $memberStat['other_count'] }}</div>
                                        <div class="text-xs text-gray-600">Other</div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <i class="fas fa-users text-4xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Team Member Assignments</h3>
                        <p class="text-gray-600">No team members have ClickUp list assignments yet.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Progress Insights & Reporting -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">Progress Insights & Reporting</h2>
                        <p class="text-sm text-gray-600 mt-1">Real-time ClickUp task progress for team members</p>
                    </div>
                    <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                        <div class="flex items-center space-x-2">
                            <label for="start-date" class="text-sm font-medium text-gray-700">From:</label>
                            <input type="date" id="start-date" value="{{ $startDate }}" 
                                   class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex items-center space-x-2">
                            <label for="end-date" class="text-sm font-medium text-gray-700">To:</label>
                            <input type="date" id="end-date" value="{{ $endDate }}" 
                                   class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <button onclick="loadTeamMemberProgress()" 
                                class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors duration-200">
                            <i class="fas fa-sync-alt mr-2"></i>
                            Load Progress
                        </button>
                    </div>
                </div>
            </div>
            <div id="team-member-progress-container" class="p-6">
                <div class="text-center py-12 text-gray-500">
                    <i class="fas fa-chart-line text-3xl mb-3"></i>
                    <p>Click "Load Progress" to view team member ClickUp progress data</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Report Modal -->
<div id="emailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-green-600 to-emerald-600 rounded-lg flex items-center justify-center text-white">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Team Member ClickUp Email Report</h3>
                        <p class="text-sm text-gray-600">Generate and send progress insights via email</p>
                    </div>
                </div>
                <button onclick="closeEmailModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="px-6 py-6 space-y-6">
            <!-- Report Type Selection -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">Report Type</label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="relative">
                        <input type="radio" id="reportTypeIndividual" name="reportType" value="individual" class="sr-only" checked>
                        <label for="reportTypeIndividual" class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-green-300 transition-colors report-type-option">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-user text-blue-600"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">Individual Report</div>
                                    <div class="text-sm text-gray-600">Report for specific team member</div>
                                </div>
                            </div>
                        </label>
                    </div>
                    <div class="relative">
                        <input type="radio" id="reportTypeManagement" name="reportType" value="management" class="sr-only">
                        <label for="reportTypeManagement" class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-green-300 transition-colors report-type-option">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-users text-purple-600"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">Management Summary</div>
                                    <div class="text-sm text-gray-600">Consolidated team overview</div>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Team Member Selection (for individual reports) -->
            <div id="teamMemberSelection" class="space-y-3">
                <label class="block text-sm font-medium text-gray-700">Select Team Member</label>
                <select id="selectedTeamMember" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    <option value="">Choose a team member...</option>
                    @foreach($teamMemberStats as $memberStat)
                        <option value="{{ $memberStat->id }}">{{ $memberStat->name }} ({{ $memberStat->clickup_lists_count }} lists)</option>
                    @endforeach
                </select>
            </div>

            <!-- Date Range Selection -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="emailStartDate" class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                    <input type="date" id="emailStartDate" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>
                <div>
                    <label for="emailEndDate" class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                    <input type="date" id="emailEndDate" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>
            </div>

            <!-- Recipients Selection -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">Email Recipients</label>
                <div class="space-y-2 max-h-32 overflow-y-auto border border-gray-200 rounded-lg p-3">
                    @foreach($teamMemberStats as $member)
                        <label class="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded">
                            <input type="checkbox" class="recipient-checkbox rounded border-gray-300 text-green-600 focus:ring-green-500"
                                   value="{{ $member->email }}" data-name="{{ $member->name }}">
                            <div class="flex items-center space-x-2">
                                <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                                    {{ substr($member->name, 0, 1) }}
                                </div>
                                <span class="text-sm text-gray-900">{{ $member->name }}</span>
                                <span class="text-xs text-gray-500">({{ $member->email }})</span>
                            </div>
                        </label>
                    @endforeach
                </div>
                <div class="mt-2">
                    <button type="button" onclick="toggleAllRecipients()" class="text-sm text-green-600 hover:text-green-700 font-medium">
                        Select All / Deselect All
                    </button>
                </div>
            </div>

            <!-- Custom Message -->
            <div>
                <label for="customMessage" class="block text-sm font-medium text-gray-700 mb-2">Custom Message (Optional)</label>
                <textarea id="customMessage" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                          placeholder="Add a custom message to include in the email report..."></textarea>
                <p class="text-xs text-gray-500 mt-1">This message will appear at the top of the email report.</p>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 justify-end">
            <button onclick="closeEmailModal()"
                    class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                Cancel
            </button>
            <button id="sendEmailBtn" onclick="sendEmailReport()"
                    class="px-6 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-200 shadow-sm hover:shadow-md">
                <i class="fas fa-paper-plane mr-2"></i>
                Generate Email Report
            </button>
        </div>
    </div>
</div>

@push('scripts')
<script>
function loadTeamMemberProgress() {
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;
    const container = document.getElementById('team-member-progress-container');
    
    // Show loading state
    container.innerHTML = `
        <div class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
            <p class="text-gray-600">Loading team member progress data...</p>
        </div>
    `;
    
    // Fetch progress data
    fetch(`{{ route('reports.team-member-clickup-progress') }}?start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTeamMemberProgressData(data.data, data.date_range);
            } else {
                container.innerHTML = `
                    <div class="text-center py-12 text-red-600">
                        <i class="fas fa-exclamation-triangle text-3xl mb-3"></i>
                        <p>Error loading progress data: ${data.message}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            container.innerHTML = `
                <div class="text-center py-12 text-red-600">
                    <i class="fas fa-exclamation-triangle text-3xl mb-3"></i>
                    <p>Error loading progress data. Please try again.</p>
                </div>
            `;
        });
}

function displayTeamMemberProgressData(progressData, dateRange) {
    const container = document.getElementById('team-member-progress-container');
    
    if (!progressData || progressData.length === 0) {
        container.innerHTML = `
            <div class="text-center py-12 text-gray-500">
                <i class="fas fa-chart-line text-3xl mb-3"></i>
                <p>No progress data available for the selected date range</p>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Progress Report</h3>
                <span class="text-sm text-gray-600">
                    ${dateRange.start_date} to ${dateRange.end_date}
                </span>
            </div>
        </div>
        <div class="space-y-6">
    `;
    
    progressData.forEach(memberData => {
        const member = memberData.team_member;
        const summary = memberData.summary;
        
        html += `
            <div class="border border-gray-200 rounded-lg overflow-hidden">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                                ${member.name.charAt(0)}
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">${member.name}</h4>
                                <p class="text-sm text-gray-600">${member.position}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4 text-sm">
                            <span class="text-green-600 font-medium">${summary.features_count} Features</span>
                            <span class="text-red-600 font-medium">${summary.bugs_count} Bugs</span>
                            <span class="text-gray-600 font-medium">${summary.other_count} Other</span>
                        </div>
                    </div>
                </div>
                <div class="p-6">
        `;
        
        if (memberData.assignments && memberData.assignments.length > 0) {
            memberData.assignments.forEach(assignment => {
                const progress = assignment.progress;
                const listTypeBadge = getListTypeBadge(assignment.list_type);
                
                html += `
                    <div class="mb-6 last:mb-0 border border-gray-100 rounded-lg p-4">
                        <div class="flex items-start justify-between mb-3">
                            <div>
                                <div class="flex items-center space-x-2 mb-1">
                                    ${listTypeBadge}
                                    <span class="text-sm font-medium text-gray-900">${assignment.hierarchical_path}</span>
                                </div>
                                ${assignment.notes ? `<p class="text-sm text-gray-600">${assignment.notes}</p>` : ''}
                            </div>
                        </div>
                `;
                
                if (progress) {
                    html += `
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                            <div class="text-center">
                                <div class="text-lg font-semibold text-blue-600">${progress.total_tasks || 0}</div>
                                <div class="text-xs text-gray-600">Total Tasks</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-green-600">${progress.completed_tasks || 0}</div>
                                <div class="text-xs text-gray-600">Completed</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-yellow-600">${progress.in_progress_tasks || 0}</div>
                                <div class="text-xs text-gray-600">In Progress</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-gray-600">${progress.completion_rate || 0}%</div>
                                <div class="text-xs text-gray-600">Completion</div>
                            </div>
                        </div>
                    `;

                    // Display custom status breakdown if available
                    if (progress.status_breakdown && Object.keys(progress.status_breakdown).length > 0) {
                        html += `
                            <div class="mt-4 pt-4 border-t border-gray-100">
                                <h5 class="text-sm font-medium text-gray-700 mb-3">Status Breakdown</h5>
                                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                        `;

                        Object.entries(progress.status_breakdown).forEach(([status, count]) => {
                            const statusColor = getStatusColor(status);
                            html += `
                                <div class="text-center p-2 bg-gray-50 rounded-lg">
                                    <div class="text-sm font-semibold ${statusColor}">${count}</div>
                                    <div class="text-xs text-gray-600 capitalize">${status}</div>
                                </div>
                            `;
                        });

                        html += `
                                </div>
                            </div>
                        `;
                    }
                } else if (assignment.error) {
                    html += `
                        <div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                            <p class="text-sm text-red-600">Error: ${assignment.error}</p>
                        </div>
                    `;
                } else {
                    html += `
                        <div class="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-md">
                            <p class="text-sm text-gray-600">No progress data available</p>
                        </div>
                    `;
                }
                
                html += `</div>`;
            });
        } else {
            html += `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-tasks text-2xl mb-2"></i>
                    <p>No ClickUp assignments found</p>
                </div>
            `;
        }
        
        html += `
                </div>
            </div>
        `;
    });
    
    html += `</div>`;
    
    container.innerHTML = html;
}

function getListTypeBadge(listType) {
    const badges = {
        'features': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Features</span>',
        'bugs': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Bugs</span>',
        'other': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Other</span>'
    };
    return badges[listType] || badges['other'];
}

function getStatusColor(status) {
    const statusLower = status.toLowerCase();

    // Closed/completed statuses
    if (statusLower.includes('complete') || statusLower.includes('done') ||
        statusLower.includes('closed') || statusLower.includes('approved') ||
        statusLower.includes('accepted')) {
        return 'text-green-600';
    }

    // In progress statuses
    if (statusLower.includes('progress') || statusLower.includes('active') ||
        statusLower.includes('working') || statusLower.includes('development')) {
        return 'text-blue-600';
    }

    // Review/testing statuses
    if (statusLower.includes('review') || statusLower.includes('testing') ||
        statusLower.includes('validation') || statusLower.includes('pending')) {
        return 'text-purple-600';
    }

    // Hold/blocked statuses
    if (statusLower.includes('hold') || statusLower.includes('blocked') ||
        statusLower.includes('waiting') || statusLower.includes('discussion')) {
        return 'text-yellow-600';
    }

    // Bug/error statuses
    if (statusLower.includes('bug') || statusLower.includes('error') ||
        statusLower.includes('rejected') || statusLower.includes('failed')) {
        return 'text-red-600';
    }

    // Default for open/todo statuses
    return 'text-gray-600';
}

// Email Modal Functions
let selectedRecipients = [];

function openEmailModal() {
    // Set default date range (last 30 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    document.getElementById('emailStartDate').value = startDate.toISOString().split('T')[0];
    document.getElementById('emailEndDate').value = endDate.toISOString().split('T')[0];

    // Show modal
    document.getElementById('emailModal').classList.remove('hidden');

    // Initialize report type selection
    updateReportTypeSelection();
}

function closeEmailModal() {
    document.getElementById('emailModal').classList.add('hidden');

    // Reset form
    document.getElementById('selectedTeamMember').value = '';
    document.getElementById('customMessage').value = '';

    // Clear recipients
    selectedRecipients = [];
    document.querySelectorAll('.recipient-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });

    // Reset report type to individual
    document.getElementById('reportTypeIndividual').checked = true;
    updateReportTypeSelection();
}

function updateReportTypeSelection() {
    const isIndividual = document.getElementById('reportTypeIndividual').checked;
    const teamMemberSelection = document.getElementById('teamMemberSelection');

    if (isIndividual) {
        teamMemberSelection.style.display = 'block';
    } else {
        teamMemberSelection.style.display = 'none';
    }

    // Update visual selection
    document.querySelectorAll('.report-type-option').forEach(option => {
        const input = option.querySelector('input[type="radio"]');
        if (input.checked) {
            option.classList.add('border-green-500', 'bg-green-50');
            option.classList.remove('border-gray-200');
        } else {
            option.classList.remove('border-green-500', 'bg-green-50');
            option.classList.add('border-gray-200');
        }
    });
}

function toggleAllRecipients() {
    const checkboxes = document.querySelectorAll('.recipient-checkbox');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(checkbox => {
        checkbox.checked = !allChecked;
    });
}

function sendEmailReport() {
    const reportType = document.querySelector('input[name="reportType"]:checked').value;
    const startDate = document.getElementById('emailStartDate').value;
    const endDate = document.getElementById('emailEndDate').value;
    const customMessage = document.getElementById('customMessage').value;

    // Validate inputs
    if (!startDate || !endDate) {
        alert('Please select both start and end dates.');
        return;
    }

    if (new Date(startDate) > new Date(endDate)) {
        alert('Start date must be before or equal to end date.');
        return;
    }

    // Get selected recipients
    const selectedCheckboxes = document.querySelectorAll('.recipient-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        alert('Please select at least one recipient.');
        return;
    }

    selectedRecipients = Array.from(selectedCheckboxes).map(cb => ({
        email: cb.value,
        name: cb.dataset.name
    }));

    // Validate team member selection for individual reports
    if (reportType === 'individual') {
        const selectedTeamMember = document.getElementById('selectedTeamMember').value;
        if (!selectedTeamMember) {
            alert('Please select a team member for individual report.');
            return;
        }
    }

    // Show loading state
    const sendBtn = document.getElementById('sendEmailBtn');
    const originalText = sendBtn.innerHTML;
    sendBtn.disabled = true;
    sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generating...';

    try {
        // Generate email report
        generateTeamMemberEmailReport(reportType, startDate, endDate, customMessage, () => {
            // Reset button state
            sendBtn.disabled = false;
            sendBtn.innerHTML = originalText;
        });
    } catch (error) {
        console.error('Error generating email report:', error);
        alert('An error occurred while generating the email report.');
        sendBtn.disabled = false;
        sendBtn.innerHTML = originalText;
    }
}

function generateTeamMemberEmailReport(reportType, startDate, endDate, customMessage, callback) {
    if (reportType === 'individual') {
        generateIndividualReport(startDate, endDate, customMessage, callback);
    } else {
        generateManagementSummaryReport(startDate, endDate, customMessage, callback);
    }
}

function generateIndividualReport(startDate, endDate, customMessage, callback) {
    const selectedTeamMemberId = document.getElementById('selectedTeamMember').value;

    // Fetch individual team member progress data
    fetch(`/api/team-members/${selectedTeamMemberId}/clickup-progress?start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createIndividualMailtoLink(data.data, startDate, endDate, customMessage);
            } else {
                alert('Error fetching team member progress data: ' + data.message);
            }
            callback();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error fetching team member progress data. Please try again.');
            callback();
        });
}

function generateManagementSummaryReport(startDate, endDate, customMessage, callback) {
    // Fetch consolidated team progress data
    fetch(`/api/team-members/clickup-progress-summary?start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createManagementSummaryMailtoLink(data.data, startDate, endDate, customMessage);
            } else {
                alert('Error fetching team progress data: ' + data.message);
            }
            callback();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error fetching team progress data. Please try again.');
            callback();
        });
}

function createIndividualMailtoLink(memberData, startDate, endDate, customMessage) {
    // Format dates for display
    const startDateFormatted = new Date(startDate).toLocaleDateString('en-US', {
        year: 'numeric', month: 'short', day: 'numeric'
    });
    const endDateFormatted = new Date(endDate).toLocaleDateString('en-US', {
        year: 'numeric', month: 'short', day: 'numeric'
    });

    // Create subject
    const subject = `ClickUp Progress Report for ${memberData.team_member.name} - ${startDateFormatted} to ${endDateFormatted}`;

    // Create HTML email body
    const htmlEmailBody = createIndividualEmailBody(memberData, startDate, endDate, customMessage);

    // Create recipients list
    const recipients = selectedRecipients.map(r => r.email).join(',');

    // Check if mailto URL would be too long
    const testMailtoUrl = `mailto:${recipients}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(htmlEmailBody)}`;

    if (testMailtoUrl.length > 1500) {
        // Show copy to clipboard option instead
        showCopyToClipboardOption(recipients, subject, htmlEmailBody);
    } else {
        // Create and trigger mailto link
        window.location.href = testMailtoUrl;

        // Show success message and close modal
        setTimeout(() => {
            alert('Email client opened with pre-filled report. Please review and send the email.');
            closeEmailModal();
        }, 500);
    }
}

function createManagementSummaryMailtoLink(teamData, startDate, endDate, customMessage) {
    // Format dates for display
    const startDateFormatted = new Date(startDate).toLocaleDateString('en-US', {
        year: 'numeric', month: 'short', day: 'numeric'
    });
    const endDateFormatted = new Date(endDate).toLocaleDateString('en-US', {
        year: 'numeric', month: 'short', day: 'numeric'
    });

    // Create subject
    const subject = `Team ClickUp Progress Summary - ${startDateFormatted} to ${endDateFormatted}`;

    // Create HTML email body
    const htmlEmailBody = createManagementSummaryEmailBody(teamData, startDate, endDate, customMessage);

    // Create recipients list
    const recipients = selectedRecipients.map(r => r.email).join(',');

    // Check if mailto URL would be too long
    const testMailtoUrl = `mailto:${recipients}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(htmlEmailBody)}`;

    if (testMailtoUrl.length > 1500) {
        // Show copy to clipboard option instead
        showCopyToClipboardOption(recipients, subject, htmlEmailBody);
    } else {
        // Create and trigger mailto link
        window.location.href = testMailtoUrl;

        // Show success message and close modal
        setTimeout(() => {
            alert('Email client opened with pre-filled report. Please review and send the email.');
            closeEmailModal();
        }, 500);
    }
}

// Event listeners for report type selection
document.addEventListener('DOMContentLoaded', function() {
    // Report type radio buttons
    document.querySelectorAll('input[name="reportType"]').forEach(radio => {
        radio.addEventListener('change', updateReportTypeSelection);
    });
});

function createIndividualEmailBody(memberData, startDate, endDate, customMessage) {
    const startDateFormatted = new Date(startDate).toLocaleDateString('en-US', {
        year: 'numeric', month: 'long', day: 'numeric'
    });
    const endDateFormatted = new Date(endDate).toLocaleDateString('en-US', {
        year: 'numeric', month: 'long', day: 'numeric'
    });

    const member = memberData.team_member;
    const assignments = memberData.assignments || [];

    // Calculate overall statistics
    let totalTasks = 0, totalCompleted = 0, totalInProgress = 0, totalOpen = 0;
    assignments.forEach(assignment => {
        if (assignment.progress) {
            totalTasks += assignment.progress.total_tasks || 0;
            totalCompleted += assignment.progress.completed_tasks || 0;
            totalInProgress += assignment.progress.in_progress_tasks || 0;
            totalOpen += assignment.progress.open_tasks || 0;
        }
    });

    const completionRate = totalTasks > 0 ? Math.round((totalCompleted / totalTasks) * 100) : 0;

    let emailBody = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team Member ClickUp Progress Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #374151; margin: 0; padding: 20px; background-color: #f9fafb; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 700; }
        .subtitle { margin: 8px 0 0 0; opacity: 0.9; font-size: 16px; }
        .custom-message { background: #fef3c7; border-left: 4px solid #f59e0b; padding: 16px; margin: 20px; border-radius: 6px; }
        .section { padding: 30px; border-bottom: 1px solid #e5e7eb; }
        .section:last-child { border-bottom: none; }
        .section h2 { margin: 0 0 20px 0; font-size: 20px; font-weight: 600; color: #1f2937; }
        .stats-grid { display: table; width: 100%; border-collapse: collapse; }
        .stat-row { display: table-row; }
        .stat-cell { display: table-cell; padding: 15px; text-align: center; border: 1px solid #e5e7eb; background: #f9fafb; }
        .stat-value { font-size: 24px; font-weight: 700; color: #1f2937; }
        .stat-label { font-size: 14px; color: #6b7280; margin-top: 4px; }
        .assignment-item { border: 1px solid #e5e7eb; border-radius: 8px; margin-bottom: 20px; overflow: hidden; }
        .assignment-header { background: #f9fafb; padding: 16px; border-bottom: 1px solid #e5e7eb; }
        .assignment-name { font-weight: 600; color: #1f2937; margin-bottom: 4px; }
        .assignment-type { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; text-transform: uppercase; }
        .type-features { background: #dbeafe; color: #1e40af; }
        .type-bugs { background: #fee2e2; color: #dc2626; }
        .type-other { background: #f3e8ff; color: #7c3aed; }
        .progress-bar { background: #e5e7eb; height: 8px; border-radius: 4px; overflow: hidden; margin: 12px 0; }
        .progress-fill { background: linear-gradient(90deg, #10b981, #059669); height: 100%; transition: width 0.3s ease; }
        .progress-stats { display: table; width: 100%; }
        .progress-stat { display: table-cell; text-align: center; padding: 8px; }
        .footer { background: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👤 Team Member ClickUp Progress Report</h1>
            <p class="subtitle">${member.name} • ${startDateFormatted} - ${endDateFormatted}</p>
        </div>`;

    if (customMessage) {
        emailBody += `
        <div class="custom-message">
            <strong>Message:</strong> ${customMessage}
        </div>`;
    }

    emailBody += `
        <div class="section">
            <h2>📊 Overall Progress Summary</h2>
            <div class="stats-grid">
                <div class="stat-row">
                    <div class="stat-cell">
                        <div class="stat-value">${totalTasks}</div>
                        <div class="stat-label">Total Tasks</div>
                    </div>
                    <div class="stat-cell">
                        <div class="stat-value" style="color: #10b981;">${totalCompleted}</div>
                        <div class="stat-label">Completed</div>
                    </div>
                    <div class="stat-cell">
                        <div class="stat-value" style="color: #f59e0b;">${totalInProgress}</div>
                        <div class="stat-label">In Progress</div>
                    </div>
                    <div class="stat-cell">
                        <div class="stat-value" style="color: #6b7280;">${totalOpen}</div>
                        <div class="stat-label">Open</div>
                    </div>
                    <div class="stat-cell">
                        <div class="stat-value" style="color: #8b5cf6;">${completionRate}%</div>
                        <div class="stat-label">Completion Rate</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📝 ClickUp List Assignments</h2>`;

    if (assignments.length > 0) {
        assignments.forEach(assignment => {
            const progress = assignment.progress || {};
            const assignmentCompletionRate = progress.total_tasks > 0 ? Math.round((progress.completed_tasks / progress.total_tasks) * 100) : 0;

            emailBody += `
            <div class="assignment-item">
                <div class="assignment-header">
                    <div class="assignment-name">${assignment.list_name}</div>
                    <span class="assignment-type type-${assignment.list_type}">${assignment.list_type_name}</span>
                    <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                        🗂️ ${assignment.hierarchical_path}
                    </div>
                </div>
                <div style="padding: 16px;">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${assignmentCompletionRate}%"></div>
                    </div>
                    <div class="progress-stats">
                        <div class="progress-stat">
                            <strong>${progress.total_tasks || 0}</strong><br>
                            <small style="color: #6b7280;">Total</small>
                        </div>
                        <div class="progress-stat">
                            <strong style="color: #10b981;">${progress.completed_tasks || 0}</strong><br>
                            <small style="color: #6b7280;">Completed</small>
                        </div>
                        <div class="progress-stat">
                            <strong style="color: #f59e0b;">${progress.in_progress_tasks || 0}</strong><br>
                            <small style="color: #6b7280;">In Progress</small>
                        </div>
                        <div class="progress-stat">
                            <strong style="color: #6b7280;">${progress.open_tasks || 0}</strong><br>
                            <small style="color: #6b7280;">Open</small>
                        </div>
                        <div class="progress-stat">
                            <strong style="color: #8b5cf6;">${assignmentCompletionRate}%</strong><br>
                            <small style="color: #6b7280;">Complete</small>
                        </div>
                    </div>`;

            // Add custom status breakdown if available
            if (progress.status_breakdown && Object.keys(progress.status_breakdown).length > 0) {
                emailBody += `
                    <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #e5e7eb;">
                        <div style="font-weight: 600; margin-bottom: 8px; color: #374151;">Custom Status Breakdown:</div>
                        <div style="display: table; width: 100%;">`;

                Object.entries(progress.status_breakdown).forEach(([status, count]) => {
                    emailBody += `
                            <div style="display: table-cell; text-align: center; padding: 4px;">
                                <div style="font-weight: 600; color: #1f2937;">${count}</div>
                                <div style="font-size: 12px; color: #6b7280; text-transform: capitalize;">${status}</div>
                            </div>`;
                });

                emailBody += `
                        </div>
                    </div>`;
            }

            emailBody += `
                </div>
            </div>`;
        });
    } else {
        emailBody += `<p style="text-align: center; color: #6b7280; font-style: italic;">No ClickUp lists are currently assigned to this team member.</p>`;
    }

    emailBody += `
        </div>
        <div class="footer">
            <p>This report was generated on ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: '2-digit' })}</p>
            <p>Generated by the Product Management System</p>
        </div>
    </div>
</body>
</html>`;

    return emailBody;
}

function createManagementSummaryEmailBody(teamData, startDate, endDate, customMessage) {
    const startDateFormatted = new Date(startDate).toLocaleDateString('en-US', {
        year: 'numeric', month: 'long', day: 'numeric'
    });
    const endDateFormatted = new Date(endDate).toLocaleDateString('en-US', {
        year: 'numeric', month: 'long', day: 'numeric'
    });

    const teamMembers = teamData.team_members || [];
    const overallStats = teamData.overall_stats || {};

    let emailBody = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team ClickUp Progress Summary</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #374151; margin: 0; padding: 20px; background-color: #f9fafb; }
        .container { max-width: 900px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .header { background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 700; }
        .subtitle { margin: 8px 0 0 0; opacity: 0.9; font-size: 16px; }
        .custom-message { background: #fef3c7; border-left: 4px solid #f59e0b; padding: 16px; margin: 20px; border-radius: 6px; }
        .section { padding: 30px; border-bottom: 1px solid #e5e7eb; }
        .section:last-child { border-bottom: none; }
        .section h2 { margin: 0 0 20px 0; font-size: 20px; font-weight: 600; color: #1f2937; }
        .stats-grid { display: table; width: 100%; border-collapse: collapse; }
        .stat-row { display: table-row; }
        .stat-cell { display: table-cell; padding: 15px; text-align: center; border: 1px solid #e5e7eb; background: #f9fafb; }
        .stat-value { font-size: 24px; font-weight: 700; color: #1f2937; }
        .stat-label { font-size: 14px; color: #6b7280; margin-top: 4px; }
        .member-item { border: 1px solid #e5e7eb; border-radius: 8px; margin-bottom: 20px; overflow: hidden; }
        .member-header { background: #f9fafb; padding: 16px; border-bottom: 1px solid #e5e7eb; }
        .member-name { font-weight: 600; color: #1f2937; margin-bottom: 4px; }
        .member-stats { display: table; width: 100%; }
        .member-stat { display: table-cell; text-align: center; padding: 12px; }
        .footer { background: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👥 Team ClickUp Progress Summary</h1>
            <p class="subtitle">${startDateFormatted} - ${endDateFormatted}</p>
        </div>`;

    if (customMessage) {
        emailBody += `
        <div class="custom-message">
            <strong>Message:</strong> ${customMessage}
        </div>`;
    }

    emailBody += `
        <div class="section">
            <h2>📊 Team Overview</h2>
            <div class="stats-grid">
                <div class="stat-row">
                    <div class="stat-cell">
                        <div class="stat-value">${overallStats.total_team_members || 0}</div>
                        <div class="stat-label">Team Members</div>
                    </div>
                    <div class="stat-cell">
                        <div class="stat-value">${overallStats.total_assignments || 0}</div>
                        <div class="stat-label">Total Assignments</div>
                    </div>
                    <div class="stat-cell">
                        <div class="stat-value">${overallStats.total_tasks || 0}</div>
                        <div class="stat-label">Total Tasks</div>
                    </div>
                    <div class="stat-cell">
                        <div class="stat-value" style="color: #10b981;">${overallStats.total_completed || 0}</div>
                        <div class="stat-label">Completed</div>
                    </div>
                    <div class="stat-cell">
                        <div class="stat-value" style="color: #8b5cf6;">${overallStats.completion_rate || 0}%</div>
                        <div class="stat-label">Completion Rate</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>👤 Individual Team Member Progress</h2>`;

    if (teamMembers.length > 0) {
        teamMembers.forEach(member => {
            const memberStats = member.stats || {};
            const completionRate = memberStats.total_tasks > 0 ? Math.round((memberStats.completed_tasks / memberStats.total_tasks) * 100) : 0;

            emailBody += `
            <div class="member-item">
                <div class="member-header">
                    <div class="member-name">${member.name}</div>
                    <div style="font-size: 14px; color: #6b7280;">${member.position || 'Team Member'} • ${memberStats.assignments_count || 0} assignments</div>
                </div>
                <div style="padding: 16px;">
                    <div class="member-stats">
                        <div class="member-stat">
                            <strong>${memberStats.total_tasks || 0}</strong><br>
                            <small style="color: #6b7280;">Total Tasks</small>
                        </div>
                        <div class="member-stat">
                            <strong style="color: #10b981;">${memberStats.completed_tasks || 0}</strong><br>
                            <small style="color: #6b7280;">Completed</small>
                        </div>
                        <div class="member-stat">
                            <strong style="color: #f59e0b;">${memberStats.in_progress_tasks || 0}</strong><br>
                            <small style="color: #6b7280;">In Progress</small>
                        </div>
                        <div class="member-stat">
                            <strong style="color: #6b7280;">${memberStats.open_tasks || 0}</strong><br>
                            <small style="color: #6b7280;">Open</small>
                        </div>
                        <div class="member-stat">
                            <strong style="color: #8b5cf6;">${completionRate}%</strong><br>
                            <small style="color: #6b7280;">Completion Rate</small>
                        </div>
                    </div>

                    <div style="margin-top: 12px; font-size: 14px; color: #6b7280;">
                        <strong>List Types:</strong>
                        ${memberStats.features_count || 0} Features,
                        ${memberStats.bugs_count || 0} Bugs,
                        ${memberStats.other_count || 0} Other
                    </div>
                </div>
            </div>`;
        });
    } else {
        emailBody += `<p style="text-align: center; color: #6b7280; font-style: italic;">No team members have ClickUp assignments.</p>`;
    }

    emailBody += `
        </div>
        <div class="footer">
            <p>This report was generated on ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: '2-digit' })}</p>
            <p>Generated by the Product Management System</p>
        </div>
    </div>
</body>
</html>`;

    return emailBody;
}

function showCopyToClipboardOption(recipients, subject, htmlEmailBody) {
    const emailContent = `To: ${recipients}\nSubject: ${subject}\n\n${htmlEmailBody}`;

    // Try to copy to clipboard
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(emailContent).then(() => {
            alert('Email content copied to clipboard! You can paste it into your email client.');
        }).catch(() => {
            showManualCopyDialog(emailContent);
        });
    } else {
        showManualCopyDialog(emailContent);
    }

    closeEmailModal();
}

function showManualCopyDialog(emailContent) {
    const dialog = document.createElement('div');
    dialog.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
    dialog.innerHTML = `
        <div class="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                <h3 class="text-lg font-semibold text-gray-900">Copy Email Content</h3>
                <p class="text-sm text-gray-600">Copy the content below and paste it into your email client</p>
            </div>
            <div class="p-6">
                <textarea readonly class="w-full h-96 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none"
                          onclick="this.select()">${emailContent}</textarea>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end">
                <button onclick="this.closest('.fixed').remove()"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Close
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(dialog);

    // Auto-select the textarea content
    const textarea = dialog.querySelector('textarea');
    textarea.focus();
    textarea.select();
}

// Auto-load progress on page load
document.addEventListener('DOMContentLoaded', function() {
    loadTeamMemberProgress();
});
</script>
@endpush
@endsection
