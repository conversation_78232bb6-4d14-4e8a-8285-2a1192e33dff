@extends('layouts.app')

@section('page-title', 'Team Member ClickUp Insights')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
    <!-- Enhanced Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-600 to-indigo-700 rounded-xl flex items-center justify-center text-white shadow-lg">
                        <i class="fas fa-users-cog text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Team Member ClickUp Insights</h1>
                        <p class="text-gray-600 mt-1">Track team member ClickUp assignments and progress</p>
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
                    <a href="{{ route('reports.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Reports
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Team Member ClickUp Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Connected Members</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $teamClickupStats['connected_team_members'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-check text-purple-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Assignments</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $teamClickupStats['total_assignments'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-tasks text-blue-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Features Lists</p>
                        <p class="text-2xl font-bold text-green-600">{{ $teamClickupStats['features_lists'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-star text-green-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Bug Lists</p>
                        <p class="text-2xl font-bold text-red-600">{{ $teamClickupStats['bugs_lists'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-bug text-red-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Other Lists</p>
                        <p class="text-2xl font-bold text-gray-600">{{ $teamClickupStats['other_lists'] }}</p>
                    </div>
                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-list text-gray-600"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Team Member Assignments Overview -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Team Member Assignments Overview</h2>
                <p class="text-sm text-gray-600 mt-1">ClickUp list assignments by team member</p>
            </div>
            <div class="p-6">
                @if($teamMemberStats->count() > 0)
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        @foreach($teamMemberStats as $memberStat)
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow duration-200">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                                            {{ substr($memberStat['member']->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <h3 class="font-medium text-gray-900">{{ $memberStat['member']->name }}</h3>
                                            <p class="text-sm text-gray-600">{{ $memberStat['member']->position }}</p>
                                        </div>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $memberStat['total_assignments'] }} assignments
                                    </span>
                                </div>
                                
                                <div class="grid grid-cols-3 gap-3">
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-green-600">{{ $memberStat['features_count'] }}</div>
                                        <div class="text-xs text-gray-600">Features</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-red-600">{{ $memberStat['bugs_count'] }}</div>
                                        <div class="text-xs text-gray-600">Bugs</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-gray-600">{{ $memberStat['other_count'] }}</div>
                                        <div class="text-xs text-gray-600">Other</div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <i class="fas fa-users text-4xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Team Member Assignments</h3>
                        <p class="text-gray-600">No team members have ClickUp list assignments yet.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Progress Insights & Reporting -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">Progress Insights & Reporting</h2>
                        <p class="text-sm text-gray-600 mt-1">Real-time ClickUp task progress for team members</p>
                    </div>
                    <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                        <div class="flex items-center space-x-2">
                            <label for="start-date" class="text-sm font-medium text-gray-700">From:</label>
                            <input type="date" id="start-date" value="{{ $startDate }}" 
                                   class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex items-center space-x-2">
                            <label for="end-date" class="text-sm font-medium text-gray-700">To:</label>
                            <input type="date" id="end-date" value="{{ $endDate }}" 
                                   class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <button onclick="loadTeamMemberProgress()" 
                                class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors duration-200">
                            <i class="fas fa-sync-alt mr-2"></i>
                            Load Progress
                        </button>
                    </div>
                </div>
            </div>
            <div id="team-member-progress-container" class="p-6">
                <div class="text-center py-12 text-gray-500">
                    <i class="fas fa-chart-line text-3xl mb-3"></i>
                    <p>Click "Load Progress" to view team member ClickUp progress data</p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function loadTeamMemberProgress() {
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;
    const container = document.getElementById('team-member-progress-container');
    
    // Show loading state
    container.innerHTML = `
        <div class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
            <p class="text-gray-600">Loading team member progress data...</p>
        </div>
    `;
    
    // Fetch progress data
    fetch(`{{ route('reports.team-member-clickup-progress') }}?start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTeamMemberProgressData(data.data, data.date_range);
            } else {
                container.innerHTML = `
                    <div class="text-center py-12 text-red-600">
                        <i class="fas fa-exclamation-triangle text-3xl mb-3"></i>
                        <p>Error loading progress data: ${data.message}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            container.innerHTML = `
                <div class="text-center py-12 text-red-600">
                    <i class="fas fa-exclamation-triangle text-3xl mb-3"></i>
                    <p>Error loading progress data. Please try again.</p>
                </div>
            `;
        });
}

function displayTeamMemberProgressData(progressData, dateRange) {
    const container = document.getElementById('team-member-progress-container');
    
    if (!progressData || progressData.length === 0) {
        container.innerHTML = `
            <div class="text-center py-12 text-gray-500">
                <i class="fas fa-chart-line text-3xl mb-3"></i>
                <p>No progress data available for the selected date range</p>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Progress Report</h3>
                <span class="text-sm text-gray-600">
                    ${dateRange.start_date} to ${dateRange.end_date}
                </span>
            </div>
        </div>
        <div class="space-y-6">
    `;
    
    progressData.forEach(memberData => {
        const member = memberData.team_member;
        const summary = memberData.summary;
        
        html += `
            <div class="border border-gray-200 rounded-lg overflow-hidden">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                                ${member.name.charAt(0)}
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">${member.name}</h4>
                                <p class="text-sm text-gray-600">${member.position}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4 text-sm">
                            <span class="text-green-600 font-medium">${summary.features_count} Features</span>
                            <span class="text-red-600 font-medium">${summary.bugs_count} Bugs</span>
                            <span class="text-gray-600 font-medium">${summary.other_count} Other</span>
                        </div>
                    </div>
                </div>
                <div class="p-6">
        `;
        
        if (memberData.assignments && memberData.assignments.length > 0) {
            memberData.assignments.forEach(assignment => {
                const progress = assignment.progress;
                const listTypeBadge = getListTypeBadge(assignment.list_type);
                
                html += `
                    <div class="mb-6 last:mb-0 border border-gray-100 rounded-lg p-4">
                        <div class="flex items-start justify-between mb-3">
                            <div>
                                <div class="flex items-center space-x-2 mb-1">
                                    ${listTypeBadge}
                                    <span class="text-sm font-medium text-gray-900">${assignment.hierarchical_path}</span>
                                </div>
                                ${assignment.notes ? `<p class="text-sm text-gray-600">${assignment.notes}</p>` : ''}
                            </div>
                        </div>
                `;
                
                if (progress) {
                    html += `
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                            <div class="text-center">
                                <div class="text-lg font-semibold text-blue-600">${progress.total_tasks || 0}</div>
                                <div class="text-xs text-gray-600">Total Tasks</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-green-600">${progress.completed_tasks || 0}</div>
                                <div class="text-xs text-gray-600">Completed</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-yellow-600">${progress.in_progress_tasks || 0}</div>
                                <div class="text-xs text-gray-600">In Progress</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-gray-600">${progress.completion_rate || 0}%</div>
                                <div class="text-xs text-gray-600">Completion</div>
                            </div>
                        </div>
                    `;
                } else if (assignment.error) {
                    html += `
                        <div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                            <p class="text-sm text-red-600">Error: ${assignment.error}</p>
                        </div>
                    `;
                } else {
                    html += `
                        <div class="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-md">
                            <p class="text-sm text-gray-600">No progress data available</p>
                        </div>
                    `;
                }
                
                html += `</div>`;
            });
        } else {
            html += `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-tasks text-2xl mb-2"></i>
                    <p>No ClickUp assignments found</p>
                </div>
            `;
        }
        
        html += `
                </div>
            </div>
        `;
    });
    
    html += `</div>`;
    
    container.innerHTML = html;
}

function getListTypeBadge(listType) {
    const badges = {
        'features': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Features</span>',
        'bugs': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Bugs</span>',
        'other': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Other</span>'
    };
    return badges[listType] || badges['other'];
}

// Auto-load progress on page load
document.addEventListener('DOMContentLoaded', function() {
    loadTeamMemberProgress();
});
</script>
@endpush
@endsection
