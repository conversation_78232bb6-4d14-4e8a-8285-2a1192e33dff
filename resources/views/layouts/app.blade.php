<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Taqnyat Product Manager') }}</title>

    <!-- Tailwind CSS CDN with JIT -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- jQuery for AJAX -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Alpine.js for reactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    @stack('styles')
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex">
        <!-- Enhanced Collapsible Sidebar -->
        <div id="sidebar" x-ref="sidebar" class="bg-white shadow-lg transition-all duration-300 ease-in-out min-h-screen fixed lg:relative lg:translate-x-0 transform -translate-x-full z-30"
             :class="sidebarCollapsed ? 'w-16' : 'w-64'" x-data="{ sidebarCollapsed: false }" @resize.window="$dispatch('sidebar-resize', { collapsed: sidebarCollapsed })"

            <!-- Sidebar Header -->
            <div class="flex items-center justify-between h-16 bg-gradient-to-r from-blue-600 to-indigo-700 text-white px-4">
                <div class="flex items-center" x-show="!sidebarCollapsed" x-transition>
                    <i class="fas fa-chart-line text-lg"></i>
                    <h1 class="ml-2 text-lg font-bold">Taqnyat PM</h1>
                </div>
                <div class="flex items-center justify-center w-8 h-8" x-show="sidebarCollapsed" x-transition>
                    <i class="fas fa-chart-line text-lg"></i>
                </div>

                <!-- Desktop Collapse Toggle -->
                <button @click="sidebarCollapsed = !sidebarCollapsed"
                        class="hidden lg:flex items-center justify-center w-8 h-8 rounded-lg hover:bg-white hover:bg-opacity-20 transition-colors">
                    <i class="fas fa-chevron-left text-sm transition-transform duration-300"
                       :class="sidebarCollapsed ? 'rotate-180' : ''"></i>
                </button>
            </div>

            <!-- Navigation Menu -->
            <nav class="mt-6">
                <div class="px-3 space-y-1">
                    <!-- Dashboard -->
                    <div class="relative group">
                        <a href="{{ route('dashboard') }}"
                           class="flex items-center px-3 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200 {{ request()->routeIs('dashboard') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : '' }}">
                            <i class="fas fa-tachometer-alt text-lg w-5 text-center"></i>
                            <span class="ml-3 font-medium" x-show="!sidebarCollapsed" x-transition>Dashboard</span>
                        </a>
                        <!-- Tooltip for collapsed state -->
                        <div x-show="sidebarCollapsed"
                             class="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                            Dashboard
                        </div>
                    </div>

                    <!-- Products -->
                    <div class="relative group">
                        <a href="{{ route('products.index') }}"
                           class="flex items-center px-3 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200 {{ request()->routeIs('products.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : '' }}">
                            <i class="fas fa-cube text-lg w-5 text-center"></i>
                            <span class="ml-3 font-medium" x-show="!sidebarCollapsed" x-transition>Products</span>
                        </a>
                        <div x-show="sidebarCollapsed"
                             class="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                            Products
                        </div>
                    </div>

                    <!-- Team Members -->
                    <div class="relative group">
                        <a href="{{ route('team-members.index') }}"
                           class="flex items-center px-3 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200 {{ request()->routeIs('team-members.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : '' }}">
                            <i class="fas fa-users text-lg w-5 text-center"></i>
                            <span class="ml-3 font-medium" x-show="!sidebarCollapsed" x-transition>Team Members</span>
                        </a>
                        <div x-show="sidebarCollapsed"
                             class="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                            Team Members
                        </div>
                    </div>

                    <!-- KPIs -->
                    <div class="relative group">
                        <a href="{{ route('kpis.index') }}"
                           class="flex items-center px-3 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200 {{ request()->routeIs('kpis.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : '' }}">
                            <i class="fas fa-chart-bar text-lg w-5 text-center"></i>
                            <span class="ml-3 font-medium" x-show="!sidebarCollapsed" x-transition>KPIs</span>
                        </a>
                        <div x-show="sidebarCollapsed"
                             class="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                            KPIs
                        </div>
                    </div>

                    <!-- Evaluations -->
                    <div class="relative group">
                        <a href="{{ route('evaluations.index') }}"
                           class="flex items-center px-3 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200 {{ request()->routeIs('evaluations.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : '' }}">
                            <i class="fas fa-clipboard-check text-lg w-5 text-center"></i>
                            <span class="ml-3 font-medium" x-show="!sidebarCollapsed" x-transition>Evaluations</span>
                        </a>
                        <div x-show="sidebarCollapsed"
                             class="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                            Evaluations
                        </div>
                    </div>

                    <!-- Reports -->
                    <div class="relative group">
                        <a href="{{ route('reports.index') }}"
                           class="flex items-center px-3 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200 {{ request()->routeIs('reports.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : '' }}">
                            <i class="fas fa-chart-pie text-lg w-5 text-center"></i>
                            <span class="ml-3 font-medium" x-show="!sidebarCollapsed" x-transition>Reports</span>
                        </a>
                        <div x-show="sidebarCollapsed"
                             class="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                            Reports
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="relative group">
                        <a href="{{ route('settings.index') }}"
                           class="flex items-center px-3 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200 {{ request()->routeIs('settings.*') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : '' }}">
                            <i class="fas fa-cog text-lg w-5 text-center"></i>
                            <span class="ml-3 font-medium" x-show="!sidebarCollapsed" x-transition>Settings</span>
                        </a>
                        <div x-show="sidebarCollapsed"
                             class="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                            Settings
                        </div>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col lg:ml-0" x-data="{ get sidebarCollapsed() { return this.$refs.sidebar ? this.$refs.sidebar.__x.$data.sidebarCollapsed : false; } }">
            <!-- Top Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 h-16 flex items-center justify-between px-6">
                <div class="flex items-center">
                    <button id="sidebar-toggle" class="lg:hidden text-gray-500 hover:text-gray-700 focus:outline-none">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <h2 class="ml-4 lg:ml-0 text-xl font-semibold text-gray-800">
                        @yield('page-title', 'Dashboard')
                    </h2>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- Notifications -->
                    <button class="text-gray-500 hover:text-gray-700 relative">
                        <i class="fas fa-bell text-xl"></i>
                        <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                    </button>
                    
                    <!-- User Menu -->
                    <div class="relative">
                        <button class="flex items-center text-gray-700 hover:text-gray-900">
                            <i class="fas fa-user-circle text-2xl mr-2"></i>
                            <span class="hidden md:block">{{ Auth::user()->name ?? 'User' }}</span>
                            <i class="fas fa-chevron-down ml-2 text-sm"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 p-6">
                @if(session('success'))
                    <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                        <span class="block sm:inline">{{ session('success') }}</span>
                    </div>
                @endif

                @if(session('error'))
                    <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                        <span class="block sm:inline">{{ session('error') }}</span>
                    </div>
                @endif

                @yield('content')
            </main>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden hidden"></div>

    <script>
        // Sidebar toggle functionality
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            
            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
        });

        // Close sidebar when clicking overlay
        document.getElementById('sidebar-overlay').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
        });

        // CSRF token setup for AJAX
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    </script>

    @stack('scripts')
</body>
</html>
