@extends('layouts.app')

@section('page-title', 'Create KPI')

@section('content')
<div class="max-w-4xl mx-auto">
    <div class="mb-6">
        <div class="flex items-center space-x-4">
            <a href="{{ route('kpis.index') }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Create New KPI</h1>
                <p class="text-gray-600">Define a key performance indicator for a team member</p>
            </div>
        </div>
    </div>

    <form id="kpi-form" action="{{ route('kpis.store') }}" method="POST" class="space-y-6">
        @csrf
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="team_member_id" class="block text-sm font-medium text-gray-700 mb-2">Team Member *</label>
                    <select id="team_member_id" name="team_member_id" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">Select Team Member</option>
                        @foreach($teamMembers as $member)
                            <option value="{{ $member->id }}" {{ old('team_member_id', $selectedTeamMember) == $member->id ? 'selected' : '' }}>
                                {{ $member->name }} ({{ $member->role }})
                            </option>
                        @endforeach
                    </select>
                    @error('team_member_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">KPI Name *</label>
                    <input type="text" id="name" name="name" required placeholder="e.g., Task Completion Rate"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('name') }}">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="md:col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="description" name="description" rows="3" placeholder="Describe what this KPI measures..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Measurement Details</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="metric_type" class="block text-sm font-medium text-gray-700 mb-2">Metric Type *</label>
                    <select id="metric_type" name="metric_type" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">Select Type</option>
                        <option value="percentage" {{ old('metric_type') === 'percentage' ? 'selected' : '' }}>Percentage (%)</option>
                        <option value="number" {{ old('metric_type') === 'number' ? 'selected' : '' }}>Number</option>
                        <option value="hours" {{ old('metric_type') === 'hours' ? 'selected' : '' }}>Hours</option>
                        <option value="days" {{ old('metric_type') === 'days' ? 'selected' : '' }}>Days</option>
                        <option value="score" {{ old('metric_type') === 'score' ? 'selected' : '' }}>Score</option>
                        <option value="rating" {{ old('metric_type') === 'rating' ? 'selected' : '' }}>Rating</option>
                    </select>
                    @error('metric_type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="target_value" class="block text-sm font-medium text-gray-700 mb-2">Target Value *</label>
                    <input type="number" id="target_value" name="target_value" required step="0.01" min="0"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('target_value') }}">
                    @error('target_value')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="period" class="block text-sm font-medium text-gray-700 mb-2">Evaluation Period *</label>
                    <select id="period" name="period" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">Select Period</option>
                        <option value="weekly" {{ old('period') === 'weekly' ? 'selected' : '' }}>Weekly</option>
                        <option value="monthly" {{ old('period') === 'monthly' ? 'selected' : '' }}>Monthly</option>
                        <option value="quarterly" {{ old('period') === 'quarterly' ? 'selected' : '' }}>Quarterly</option>
                    </select>
                    @error('period')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="mt-6">
                <div class="flex items-center">
                    <input type="checkbox" id="is_active" name="is_active" value="1" checked
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                    <label for="is_active" class="ml-2 block text-sm text-gray-900">
                        Active KPI (start tracking immediately)
                    </label>
                </div>
            </div>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">KPI Guidelines</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Choose specific, measurable metrics that align with team goals</li>
                            <li>Set realistic but challenging target values</li>
                            <li>Consider the evaluation period based on the nature of the work</li>
                            <li>Regular evaluations help track progress and identify improvement areas</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex justify-end space-x-4">
            <a href="{{ route('kpis.index') }}" 
               class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                Cancel
            </a>
            <button type="submit" 
                    class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 font-medium">
                <i class="fas fa-chart-line mr-2"></i>
                Create KPI
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Form submission with AJAX
    $('#kpi-form').submit(function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    window.location.href = response.redirect;
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    // Clear previous errors
                    $('.text-red-600').remove();
                    
                    // Display new errors
                    Object.keys(errors).forEach(function(key) {
                        const input = $(`[name="${key}"]`);
                        input.after(`<p class="mt-1 text-sm text-red-600">${errors[key][0]}</p>`);
                    });
                } else {
                    alert('Error creating KPI');
                }
            }
        });
    });
});
</script>
@endpush
@endsection
