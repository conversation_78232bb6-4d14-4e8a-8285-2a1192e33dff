@extends('layouts.app')

@section('page-title', 'KPIs')

@section('content')
<div class="space-y-6">
    <!-- Header with Actions -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">KPIs</h1>
            <p class="text-gray-600">Track and manage key performance indicators</p>
        </div>
        <a href="{{ route('kpis.create') }}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i class="fas fa-chart-line mr-2"></i>
            Create KPI
        </a>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <input type="text" id="search" name="search" placeholder="Search KPIs..." 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                       value="{{ request('search') }}">
            </div>
            <div>
                <label for="team_member" class="block text-sm font-medium text-gray-700 mb-2">Team Member</label>
                <select id="team_member" name="team_member" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    <option value="">All Members</option>
                    @foreach($teamMembers as $member)
                        <option value="{{ $member->id }}" {{ request('team_member') == $member->id ? 'selected' : '' }}>
                            {{ $member->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="status" name="status" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    <option value="">All Status</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="button" id="filter-btn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium transition-colors">
                    <i class="fas fa-filter mr-2"></i>
                    Filter
                </button>
                <button type="button" id="clear-filters" class="ml-2 text-gray-600 hover:text-gray-800">
                    Clear
                </button>
            </div>
        </div>
    </div>

    <!-- KPIs Table -->
    <div class="bg-white rounded-lg shadow">
        <div id="kpis-table">
            @include('kpis.partials.table', ['kpis' => $kpis])
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Filter functionality
    $('#filter-btn').click(function() {
        loadKpis();
    });

    // Clear filters
    $('#clear-filters').click(function() {
        $('#search').val('');
        $('#team_member').val('');
        $('#status').val('');
        loadKpis();
    });

    // Search on enter
    $('#search').keypress(function(e) {
        if (e.which == 13) {
            loadKpis();
        }
    });

    // Load KPIs with AJAX
    function loadKpis(page = 1) {
        const search = $('#search').val();
        const teamMember = $('#team_member').val();
        const status = $('#status').val();

        $.ajax({
            url: '{{ route("kpis.index") }}',
            type: 'GET',
            data: {
                search: search,
                team_member: teamMember,
                status: status,
                page: page
            },
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                $('#kpis-table').html(response.html);
            },
            error: function() {
                alert('Error loading KPIs');
            }
        });
    }

    // Pagination click handler
    $(document).on('click', '.pagination a', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        const page = new URL(url).searchParams.get('page');
        loadKpis(page);
    });

    // Delete KPI
    $(document).on('click', '.delete-kpi', function(e) {
        e.preventDefault();
        
        if (!confirm('Are you sure you want to delete this KPI? This will also delete all evaluations.')) {
            return;
        }

        const url = $(this).attr('href');
        
        $.ajax({
            url: url,
            type: 'DELETE',
            success: function(response) {
                if (response.success) {
                    loadKpis();
                    alert(response.message);
                }
            },
            error: function() {
                alert('Error deleting KPI');
            }
        });
    });
});
</script>
@endpush
@endsection
