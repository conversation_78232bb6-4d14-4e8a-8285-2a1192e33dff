<div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KPI</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Team Member</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Target</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            @forelse($kpis as $kpi)
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4">
                        <div>
                            <div class="text-sm font-medium text-gray-900">{{ $kpi->name }}</div>
                            @if($kpi->description)
                                <div class="text-sm text-gray-500">{{ Str::limit($kpi->description, 60) }}</div>
                            @endif
                            <div class="text-xs text-gray-400 mt-1">
                                {{ ucfirst($kpi->period) }} • {{ ucfirst($kpi->metric_type) }}
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">
                                {{ substr($kpi->teamMember->name, 0, 1) }}
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">{{ $kpi->teamMember->name }}</div>
                                <div class="text-sm text-gray-500">{{ $kpi->teamMember->role }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm font-medium text-gray-900">
                            {{ number_format($kpi->target_value, 2) }}
                        </div>
                        <div class="text-xs text-gray-500">{{ $kpi->metric_type }}</div>
                    </td>
                    <td class="px-6 py-4">
                        @if($kpi->evaluations->count() > 0)
                            @php
                                $latestEvaluation = $kpi->evaluations->first();
                                $avgPerformance = $kpi->evaluations->avg('actual_value');
                                $performancePercentage = $kpi->target_value > 0 ? ($avgPerformance / $kpi->target_value) * 100 : 0;
                            @endphp
                            <div class="text-sm font-medium text-gray-900">
                                {{ number_format($avgPerformance, 2) }} ({{ number_format($performancePercentage, 1) }}%)
                            </div>
                            <div class="text-xs text-gray-500">
                                {{ $kpi->evaluations->count() }} evaluations
                            </div>
                            @if($latestEvaluation)
                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $latestEvaluation->status_badge_color }}">
                                    {{ ucfirst(str_replace('_', ' ', $latestEvaluation->status)) }}
                                </span>
                            @endif
                        @else
                            <div class="text-sm text-gray-400">No evaluations</div>
                            <a href="{{ route('evaluations.create') }}?kpi={{ $kpi->id }}" 
                               class="text-xs text-primary-600 hover:text-primary-700">
                                Add evaluation
                            </a>
                        @endif
                    </td>
                    <td class="px-6 py-4">
                        @if($kpi->is_active)
                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                Active
                            </span>
                        @else
                            <span class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                                Inactive
                            </span>
                        @endif
                    </td>
                    <td class="px-6 py-4 text-sm font-medium">
                        <div class="flex space-x-2">
                            <a href="{{ route('kpis.show', $kpi) }}" 
                               class="text-primary-600 hover:text-primary-900" title="View">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ route('kpis.edit', $kpi) }}" 
                               class="text-yellow-600 hover:text-yellow-900" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{{ route('evaluations.create') }}?kpi={{ $kpi->id }}" 
                               class="text-green-600 hover:text-green-900" title="Add Evaluation">
                                <i class="fas fa-plus-circle"></i>
                            </a>
                            <a href="{{ route('kpis.destroy', $kpi) }}" 
                               class="text-red-600 hover:text-red-900 delete-kpi" title="Delete">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                        <i class="fas fa-chart-bar text-4xl mb-4 text-gray-300"></i>
                        <div class="text-lg font-medium">No KPIs found</div>
                        <div class="text-sm">Get started by creating your first KPI</div>
                        <a href="{{ route('kpis.create') }}" 
                           class="mt-4 inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
                            <i class="fas fa-chart-line mr-2"></i>
                            Create KPI
                        </a>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

@if($kpis->hasPages())
    <div class="px-6 py-4 border-t border-gray-200">
        {{ $kpis->links() }}
    </div>
@endif
