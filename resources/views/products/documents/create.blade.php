@extends('layouts.app')

@section('page-title', 'Upload Document')

@section('content')
<div class="max-w-4xl mx-auto">
    <div class="mb-6">
        <div class="flex items-center space-x-4">
            <a href="{{ route('products.documents.index', $product) }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Upload Document</h1>
                <p class="text-gray-600">Add a new document to {{ $product->name }}</p>
            </div>
        </div>
    </div>

    <form id="document-form" action="{{ route('products.documents.store', $product) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
        @csrf
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Document Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Document Title *</label>
                    <input type="text" id="title" name="title" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('title') }}" placeholder="e.g., User Manual, API Documentation">
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="md:col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="description" name="description" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                              placeholder="Brief description of the document content">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="uploaded_by" class="block text-sm font-medium text-gray-700 mb-2">Uploaded By</label>
                    <select id="uploaded_by" name="uploaded_by"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">Select Team Member</option>
                        @foreach($teamMembers as $member)
                            <option value="{{ $member->id }}" {{ old('uploaded_by') == $member->id ? 'selected' : '' }}>
                                {{ $member->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('uploaded_by')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Version Management *</label>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <input type="radio" id="auto_version" name="version_mode" value="auto" checked
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300">
                            <label for="auto_version" class="text-sm text-gray-700">Automatic Version</label>
                        </div>

                        <div id="auto_version_options" class="ml-7 space-y-2">
                            <select id="version_type" name="version_type"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <option value="minor" {{ old('version_type') == 'minor' ? 'selected' : '' }}>Minor Update (x.1.0)</option>
                                <option value="major" {{ old('version_type') == 'major' ? 'selected' : '' }}>Major Update (2.0.0)</option>
                                <option value="patch" {{ old('version_type') == 'patch' ? 'selected' : '' }}>Patch/Fix (x.x.1)</option>
                            </select>
                            <p class="text-xs text-gray-500">Choose the type of update this document represents</p>
                        </div>

                        <div class="flex items-center space-x-3">
                            <input type="radio" id="manual_version" name="version_mode" value="manual"
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300">
                            <label for="manual_version" class="text-sm text-gray-700">Manual Version</label>
                        </div>

                        <div id="manual_version_options" class="ml-7 space-y-2" style="display: none;">
                            <input type="text" id="manual_version_number" name="manual_version_number"
                                   placeholder="e.g., 1.0.0, 2.1.3, 3.0.0"
                                   pattern="^\d+\.\d+\.\d+$"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                   value="{{ old('manual_version_number') }}">
                            <p class="text-xs text-gray-500">Enter version in semantic format (major.minor.patch)</p>
                        </div>
                    </div>
                    @error('version_type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    @error('manual_version_number')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">File Upload</h2>
            
            <div class="space-y-4">
                <div>
                    <label for="file" class="block text-sm font-medium text-gray-700 mb-2">Document File *</label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-primary-400 transition-colors">
                        <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex text-sm text-gray-600">
                                <label for="file" class="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                                    <span>Upload a file</span>
                                    <input id="file" name="file" type="file" class="sr-only" required accept=".pdf,.doc,.docx,.txt,.md,.xlsx,.xls,.ppt,.pptx">
                                </label>
                                <p class="pl-1">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500">PDF, DOC, DOCX, TXT, MD, XLS, XLSX, PPT, PPTX up to <span id="max-file-size">{{ $maxUploadSizeMB }}MB</span></p>
                            @if($maxUploadSizeMB < 10)
                            <div class="mt-2 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-triangle text-yellow-500 text-lg"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h4 class="text-sm font-semibold text-yellow-800 mb-1">Server Upload Limit</h4>
                                        <p class="text-sm text-yellow-700 mb-2">
                                            Current server configuration limits uploads to <strong>{{ $maxUploadSizeMB }}MB</strong>.
                                            Files larger than this will be rejected.
                                        </p>
                                        <div class="text-xs text-yellow-600">
                                            <strong>Solutions:</strong>
                                            <ul class="list-disc list-inside mt-1 space-y-1">
                                                <li>Split large files into smaller parts (under {{ $maxUploadSizeMB }}MB each)</li>
                                                <li>Use file compression to reduce file size</li>
                                                <li>Contact administrator to increase server limits</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                    <div id="file-info" class="mt-2 hidden">
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <i class="fas fa-file"></i>
                            <span id="file-name"></span>
                            <span id="file-size" class="text-gray-400"></span>
                        </div>
                    </div>
                    @error('file')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <div class="flex justify-end space-x-4">
            <a href="{{ route('products.documents.index', $product) }}" 
               class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                Cancel
            </a>
            <button type="submit" 
                    class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 font-medium">
                <i class="fas fa-upload mr-2"></i>
                Upload Document
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Version mode toggle
    $('input[name="version_mode"]').change(function() {
        if ($(this).val() === 'auto') {
            $('#auto_version_options').show();
            $('#manual_version_options').hide();
            $('#version_type').prop('required', true);
            $('#manual_version_number').prop('required', false);
        } else {
            $('#auto_version_options').hide();
            $('#manual_version_options').show();
            $('#version_type').prop('required', false);
            $('#manual_version_number').prop('required', true);
        }
    });

    // Get server upload limits
    let maxUploadSize = 0;
    $.get('{{ route("products.documents.upload-limits", $product) }}', function(data) {
        maxUploadSize = data.max_upload_size_bytes;
        $('#max-file-size').text(data.max_upload_size_mb + 'MB');
    }).fail(function() {
        // Fallback to 2MB if we can't get server limits
        maxUploadSize = 2 * 1024 * 1024;
        $('#max-file-size').text('2MB');
    });

    // File input change handler
    $('#file').change(function() {
        const file = this.files[0];
        if (file) {
            $('#file-name').text(file.name);
            $('#file-size').text('(' + formatFileSize(file.size) + ')');
            $('#file-info').removeClass('hidden');

            // Check file size
            if (maxUploadSize > 0 && file.size > maxUploadSize) {
                const maxSizeMB = (maxUploadSize / 1024 / 1024).toFixed(1);
                const fileSizeMB = (file.size / 1024 / 1024).toFixed(1);

                $('#file-info').addClass('hidden');
                alert(`File too large: ${fileSizeMB}MB. Maximum allowed size is ${maxSizeMB}MB.`);
                $(this).val(''); // Clear the file input
                return;
            }
        } else {
            $('#file-info').addClass('hidden');
        }
    });

    // Form submission with AJAX
    $('#document-form').submit(function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitBtn = $(this).find('button[type="submit"]');
        
        // Show loading state
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...');
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    window.location.href = response.redirect;
                }
            },
            error: function(xhr) {
                console.log('Upload error:', xhr);

                // Clear previous errors
                $('.text-red-600').remove();

                if (xhr.status === 422) {
                    const response = xhr.responseJSON;
                    if (response.errors) {
                        // Display validation errors
                        Object.keys(response.errors).forEach(function(key) {
                            const input = $(`[name="${key}"]`);
                            input.after(`<p class="mt-1 text-sm text-red-600">${response.errors[key][0]}</p>`);
                        });
                    } else {
                        alert('Validation error: ' + (response.message || 'Please check your input'));
                    }
                } else if (xhr.status === 500) {
                    const response = xhr.responseJSON;
                    alert('Server error: ' + (response?.message || 'Internal server error occurred'));
                } else if (xhr.status === 413) {
                    alert('File too large: Please select a file smaller than 10MB');
                } else {
                    const response = xhr.responseJSON;
                    alert('Upload failed: ' + (response?.message || `HTTP ${xhr.status} error`));
                }
            },
            complete: function() {
                // Reset button state
                submitBtn.prop('disabled', false).html('<i class="fas fa-upload mr-2"></i>Upload Document');
            }
        });
    });

    // Format file size helper
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Drag and drop functionality
    const dropZone = $('.border-dashed');
    
    dropZone.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('border-primary-400 bg-primary-50');
    });
    
    dropZone.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('border-primary-400 bg-primary-50');
    });
    
    dropZone.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('border-primary-400 bg-primary-50');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            $('#file')[0].files = files;
            $('#file').trigger('change');
        }
    });
});
</script>
@endpush
@endsection
