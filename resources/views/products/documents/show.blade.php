@extends('layouts.app')

@section('page-title', $document->title)

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center space-x-4">
            <a href="{{ route('products.documents.index', $product) }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div class="flex-1">
                <h1 class="text-2xl font-bold text-gray-900">{{ $document->title }}</h1>
                <p class="text-gray-600">Document for {{ $product->name }}</p>
            </div>
            <div class="flex space-x-2">
                <a href="{{ route('products.documents.edit', [$product, $document]) }}" 
                   class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-edit mr-2"></i>
                    Edit
                </a>
                <a href="{{ route('products.documents.download', [$product, $document]) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    Download
                </a>
            </div>
        </div>
    </div>

    <!-- Document Information -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Document Details -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Document Details</h2>
                
                <div class="space-y-4">
                    @if($document->description)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <p class="text-gray-900">{{ $document->description }}</p>
                    </div>
                    @endif

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">File Name</label>
                            <p class="text-gray-900">{{ $document->file_name }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">File Type</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ strtoupper($document->file_type) }}
                            </span>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">File Size</label>
                            <p class="text-gray-900">{{ $document->file_size_human }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Version</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                v{{ $document->version }}
                            </span>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Uploaded By</label>
                            <p class="text-gray-900">
                                @if($document->uploader)
                                    {{ $document->uploader->name }}
                                @else
                                    Unknown
                                @endif
                            </p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Upload Date</label>
                            <p class="text-gray-900">{{ $document->uploaded_at->format('M d, Y \a\t g:i A') }}</p>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        @if($document->is_current_version)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                Current Version
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <i class="fas fa-archive mr-1"></i>
                                Archived Version
                            </span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Document Preview (if applicable) -->
            @if(in_array(strtolower($document->file_type), ['pdf', 'txt', 'md']))
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Document Preview</h2>
                
                @if(strtolower($document->file_type) === 'pdf')
                    <div class="border rounded-lg p-4 bg-gray-50">
                        <p class="text-gray-600 text-center">
                            <i class="fas fa-file-pdf text-red-500 text-3xl mb-2"></i><br>
                            PDF Preview not available. 
                            <a href="{{ route('products.documents.download', [$product, $document]) }}" class="text-blue-600 hover:text-blue-700">
                                Download to view
                            </a>
                        </p>
                    </div>
                @else
                    <div class="border rounded-lg p-4 bg-gray-50">
                        <p class="text-gray-600 text-center">
                            <i class="fas fa-file-alt text-gray-500 text-3xl mb-2"></i><br>
                            Text preview not available. 
                            <a href="{{ route('products.documents.download', [$product, $document]) }}" class="text-blue-600 hover:text-blue-700">
                                Download to view
                            </a>
                        </p>
                    </div>
                @endif
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="{{ route('products.documents.download', [$product, $document]) }}" 
                       class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center">
                        <i class="fas fa-download mr-2"></i>
                        Download File
                    </a>
                    
                    <a href="{{ route('products.documents.edit', [$product, $document]) }}" 
                       class="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Document
                    </a>
                    
                    @if($document->versions && $document->versions->count() > 1)
                    <a href="{{ route('products.documents.versions', [$product, $document]) }}"
                       class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center">
                        <i class="fas fa-history mr-2"></i>
                        View Versions ({{ $document->versions->count() }})
                    </a>
                    @endif
                    
                    <button onclick="confirmDelete()" 
                            class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center">
                        <i class="fas fa-trash mr-2"></i>
                        Delete Document
                    </button>
                </div>
            </div>

            <!-- Version Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Version Information</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Current Version:</span>
                        <span class="text-sm font-medium text-gray-900">v{{ $document->version }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Major:</span>
                        <span class="text-sm font-medium text-gray-900">{{ $document->version_major }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Minor:</span>
                        <span class="text-sm font-medium text-gray-900">{{ $document->version_minor }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Patch:</span>
                        <span class="text-sm font-medium text-gray-900">{{ $document->version_patch }}</span>
                    </div>
                    
                    @if($document->versions && $document->versions->count() > 1)
                    <div class="pt-2 border-t">
                        <span class="text-sm text-gray-600">Total Versions:</span>
                        <span class="text-sm font-medium text-gray-900">{{ $document->versions->count() }}</span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Related Product -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Related Product</h3>
                
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                        {{ substr($product->name, 0, 1) }}
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">{{ $product->name }}</h4>
                        <p class="text-sm text-gray-600">{{ Str::limit($product->description, 50) }}</p>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="{{ route('products.show', $product) }}" 
                       class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View Product Details →
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-2">Delete Document</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Are you sure you want to delete this document? This action cannot be undone.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <form id="deleteForm" action="{{ route('products.documents.destroy', [$product, $document]) }}" method="POST" class="inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 mr-2">
                        Delete
                    </button>
                </form>
                <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function confirmDelete() {
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});
</script>
@endpush
@endsection
