@extends('layouts.app')

@section('page-title', 'Product Documents')

@section('content')
<div class="max-w-6xl mx-auto">
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="{{ route('products.show', $product) }}" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">{{ $product->name }} - Documents</h1>
                    <p class="text-gray-600">Manage product documentation and version history</p>
                </div>
            </div>
            <a href="{{ route('products.documents.create', $product) }}" 
               class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Upload Document
            </a>
        </div>
    </div>

    @if($documents->count() > 0)
        <div class="space-y-6">
            @foreach($documents as $title => $versions)
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">{{ $title }}</h3>
                                <p class="text-sm text-gray-600">{{ $versions->count() }} version(s)</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                @php $currentVersion = $versions->where('is_current_version', true)->first() @endphp
                                @if($currentVersion)
                                    <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                        Current: v{{ $currentVersion->version }}
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="space-y-3">
                            @foreach($versions->take(3) as $document)
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div class="flex items-center space-x-4">
                                        <div class="flex-shrink-0">
                                            @php
                                                $fileIcon = match(strtolower($document->file_type)) {
                                                    'pdf' => 'pdf',
                                                    'doc', 'docx' => 'word',
                                                    'xls', 'xlsx' => 'excel',
                                                    'ppt', 'pptx' => 'powerpoint',
                                                    'txt', 'md' => 'alt',
                                                    default => 'alt'
                                                };
                                            @endphp
                                            <i class="fas fa-file-{{ $fileIcon }} text-2xl text-gray-400"></i>
                                        </div>
                                        <div>
                                            <div class="flex items-center space-x-2">
                                                <h4 class="font-medium text-gray-900">{{ $document->file_name }}</h4>
                                                <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded">
                                                    v{{ $document->version }}
                                                </span>
                                                @if($document->is_current_version)
                                                    <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">
                                                        Current
                                                    </span>
                                                @endif
                                            </div>
                                            <div class="text-sm text-gray-600 mt-1">
                                                <span>{{ $document->file_size_human }}</span>
                                                <span class="mx-2">•</span>
                                                <span>{{ $document->uploaded_at->format('M d, Y') }}</span>
                                                @if($document->uploader)
                                                    <span class="mx-2">•</span>
                                                    <span>by {{ $document->uploader->name }}</span>
                                                @endif
                                            </div>
                                            @if($document->description)
                                                <p class="text-sm text-gray-600 mt-1">{{ Str::limit($document->description, 100) }}</p>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ $document->download_url }}" 
                                           class="text-primary-600 hover:text-primary-700 p-2" title="Download">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <a href="{{ route('products.documents.show', [$product, $document]) }}" 
                                           class="text-gray-600 hover:text-gray-700 p-2" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('products.documents.edit', [$product, $document]) }}" 
                                           class="text-yellow-600 hover:text-yellow-700 p-2" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button onclick="deleteDocument({{ $document->id }})" 
                                                class="text-red-600 hover:text-red-700 p-2" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            @endforeach

                            @if($versions->count() > 3)
                                <div class="text-center">
                                    <button onclick="showAllVersions('{{ $title }}')" 
                                            class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                                        Show {{ $versions->count() - 3 }} more versions
                                    </button>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <div class="bg-white rounded-lg shadow p-12 text-center">
            <i class="fas fa-file-alt text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Documents Yet</h3>
            <p class="text-gray-600 mb-6">Start by uploading your first product document</p>
            <a href="{{ route('products.documents.create', $product) }}" 
               class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Upload First Document
            </a>
        </div>
    @endif
</div>

@push('scripts')
<script>
function deleteDocument(documentId) {
    if (!confirm('Are you sure you want to delete this document? This action cannot be undone.')) {
        return;
    }

    $.ajax({
        url: `/products/{{ $product->id }}/documents/${documentId}`,
        type: 'DELETE',
        success: function(response) {
            if (response.success) {
                location.reload();
            }
        },
        error: function() {
            alert('Error deleting document');
        }
    });
}

function showAllVersions(title) {
    // Implementation for showing all versions in a modal or expanded view
    console.log('Show all versions for:', title);
}


</script>
@endpush
@endsection
