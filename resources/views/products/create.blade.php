@extends('layouts.app')

@section('page-title', 'Create Product')

@push('styles')
<style>
    .form-section {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
    }

    .form-section:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        border-color: #cbd5e0;
    }

    .form-input {
        transition: all 0.3s ease;
        border: 2px solid #e2e8f0;
    }

    .form-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
    }

    .gradient-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .step-indicator {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .step-indicator.completed {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    .floating-save-btn {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        z-index: 50;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
        transition: all 0.3s ease;
    }

    .floating-save-btn:hover {
        transform: scale(1.05);
        box-shadow: 0 15px 35px rgba(16, 185, 129, 0.4);
    }
</style>
@endpush

@section('content')
<div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Enhanced Header -->
    <div class="gradient-header">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="{{ route('products.index') }}"
                   class="text-white hover:text-purple-200 p-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-300">
                    <i class="fas fa-arrow-left text-xl"></i>
                </a>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2">
                        <i class="fas fa-plus-circle mr-3"></i>
                        Create New Product
                    </h1>
                    <p class="text-purple-100">Build something amazing for your portfolio</p>
                </div>
            </div>
            <div class="hidden lg:flex items-center space-x-4">
                <div class="text-white text-sm">
                    <i class="fas fa-lightbulb mr-2"></i>
                    Ready to innovate?
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Steps -->
    <div class="mb-8">
        <div class="flex items-center justify-center space-x-4">
            <div class="flex items-center">
                <div class="step-indicator w-10 h-10 rounded-full flex items-center justify-center text-white font-bold">
                    1
                </div>
                <span class="ml-2 text-sm font-medium text-gray-700">Basic Info</span>
            </div>
            <div class="w-16 h-1 bg-gray-200 rounded"></div>
            <div class="flex items-center">
                <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-bold">
                    2
                </div>
                <span class="ml-2 text-sm font-medium text-gray-500">ClickUp Setup</span>
            </div>
            <div class="w-16 h-1 bg-gray-200 rounded"></div>
            <div class="flex items-center">
                <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-bold">
                    3
                </div>
                <span class="ml-2 text-sm font-medium text-gray-500">Team & Launch</span>
            </div>
        </div>
    </div>

    <form id="product-form" action="{{ route('products.store') }}" method="POST" class="space-y-6">
        @csrf
        
        <div class="form-section rounded-xl p-8 mb-8">
            <div class="flex items-center mb-6">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold mr-3">
                    1
                </div>
                <h2 class="text-xl font-bold text-gray-900">Basic Information</h2>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Product Name *</label>
                    <input type="text" id="name" name="name" required
                           class="form-input w-full px-4 py-3 rounded-lg focus:outline-none"
                           value="{{ old('name') }}" placeholder="Enter an innovative product name...">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="md:col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="description" name="description" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="phase" class="block text-sm font-medium text-gray-700 mb-2">Phase *</label>
                    <select id="phase" name="phase" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">Select Phase</option>
                        <option value="idea" {{ old('phase') === 'idea' ? 'selected' : '' }}>Idea</option>
                        <option value="in_progress" {{ old('phase') === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                        <option value="done" {{ old('phase') === 'done' ? 'selected' : '' }}>Done</option>
                    </select>
                    @error('phase')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Timeline</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                    <input type="date" id="start_date" name="start_date"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('start_date') }}">
                    @error('start_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="target_date" class="block text-sm font-medium text-gray-700 mb-2">Target Date</label>
                    <input type="date" id="target_date" name="target_date"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('target_date') }}">
                    @error('target_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="completion_date" class="block text-sm font-medium text-gray-700 mb-2">Completion Date</label>
                    <input type="date" id="completion_date" name="completion_date"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('completion_date') }}">
                    @error('completion_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- ClickUp Integration -->
        <div class="form-section rounded-xl p-8 mb-8">
            <div class="flex items-center mb-6">
                <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center text-white font-bold mr-3">
                    2
                </div>
                <h2 class="text-xl font-bold text-gray-900">ClickUp Integration</h2>
                <span class="ml-3 px-3 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                    Required
                </span>
            </div>

            <div class="grid grid-cols-1 gap-6">
                <!-- ClickUp Integration Section -->
                <div class="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg border border-purple-200">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-link text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">ClickUp Integration</h3>
                            <p class="text-sm text-gray-600">Connect this product to your ClickUp workspace</p>
                        </div>
                        <div class="ml-auto" id="clickup-status">
                            <span class="px-3 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">
                                <i class="fas fa-spinner fa-spin mr-1"></i>
                                Checking ClickUp...
                            </span>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- Space Selection -->
                        <div>
                            <label for="clickup_space_id" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-layer-group mr-1 text-purple-600"></i>
                                ClickUp Space *
                            </label>
                            <select id="clickup_space_id" name="clickup_space_id" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                <option value="">Select Space *</option>
                            </select>
                            @error('clickup_space_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Folder Selection -->
                        <div>
                            <label for="clickup_folder_id" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-folder mr-1 text-blue-600"></i>
                                ClickUp Folder
                            </label>
                            <select id="clickup_folder_id" name="clickup_folder_id"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" disabled>
                                <option value="">Select Folder (Optional)</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">Leave empty for folderless lists</p>
                        </div>

                        <!-- List Selection -->
                        <div>
                            <label for="clickup_list_id" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-list mr-1 text-green-600"></i>
                                ClickUp List *
                            </label>
                            <select id="clickup_list_id" name="clickup_list_id" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" disabled>
                                <option value="">Select List *</option>
                            </select>
                            @error('clickup_list_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div class="mt-4 p-3 bg-white rounded-md border border-gray-200">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-2"></i>
                            <div class="text-sm text-gray-700">
                                <strong>Integration Flow:</strong> Select a Space → Choose a Folder (optional) → Pick a List
                                <br>
                                <span class="text-gray-600">Every product must be connected to a ClickUp list for project management integration.</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex items-end">
                    <div class="text-sm text-gray-600">
                        <p class="font-medium mb-1">ClickUp Integration Status:</p>
                        <div id="clickup-status" class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">
                                <i class="fas fa-info-circle mr-1"></i>
                                Configure in Settings
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-section rounded-xl p-8 mb-8">
            <div class="flex items-center mb-6">
                <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg flex items-center justify-center text-white font-bold mr-3">
                    3
                </div>
                <h2 class="text-xl font-bold text-gray-900">Team Assignment</h2>
                <span class="ml-3 px-3 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                    Optional
                </span>
            </div>
            
            <div id="team-members-container">
                <div class="team-member-row grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Team Member</label>
                        <select name="team_members[]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            <option value="">Select Team Member</option>
                            @foreach($teamMembers as $member)
                                <option value="{{ $member->id }}">{{ $member->name }} ({{ $member->role }})</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Role in Product</label>
                        <input type="text" name="roles[]" placeholder="e.g., Lead Developer, Designer"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    </div>
                </div>
            </div>
            
            <button type="button" id="add-team-member" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                <i class="fas fa-plus mr-1"></i> Add Another Team Member
            </button>
        </div>

        <div class="flex justify-between items-center">
            <a href="{{ route('products.index') }}"
               class="px-6 py-3 border-2 border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-medium transition-all duration-300 hover:border-gray-400">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Products
            </a>
            <button type="submit"
                    class="px-8 py-3 bg-gradient-to-r from-green-500 to-teal-600 text-white rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                <i class="fas fa-rocket mr-2"></i>
                Launch Product
            </button>
        </div>

        <!-- Floating Save Button -->
        <button type="submit"
                class="floating-save-btn w-16 h-16 rounded-full flex items-center justify-center text-white text-xl">
            <i class="fas fa-save"></i>
        </button>
    </form>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Add team member functionality
    $('#add-team-member').click(function() {
        const container = $('#team-members-container');
        const newRow = $('.team-member-row:first').clone();
        
        // Clear values
        newRow.find('select').val('');
        newRow.find('input').val('');
        
        // Add remove button
        newRow.append(`
            <div class="flex items-end">
                <button type="button" class="remove-team-member text-red-600 hover:text-red-700 px-3 py-2">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `);
        
        container.append(newRow);
    });

    // Remove team member functionality
    $(document).on('click', '.remove-team-member', function() {
        $(this).closest('.team-member-row').remove();
    });

    // Form submission with AJAX
    $('#product-form').submit(function(e) {
        e.preventDefault();

        // Validate ClickUp list selection
        const clickupListId = $('#clickup_list_id').val();
        if (!clickupListId) {
            alert('Please select a ClickUp list before creating the product. This is required for project management integration.');
            $('#clickup_list_id').focus().addClass('border-red-500');
            return false;
        }

        const formData = new FormData(this);

        // Debug: Log form data
        console.log('Form data being sent:');
        for (let [key, value] of formData.entries()) {
            console.log(key, value);
        }

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('Success response:', response);
                if (response.success) {
                    window.location.href = response.redirect;
                }
            },
            error: function(xhr) {
                console.log('Error response:', xhr.responseJSON);
                if (xhr.status === 422) {
                    const response = xhr.responseJSON;

                    // Check if it's a ClickUp configuration error
                    if (response.message && response.message.includes('ClickUp integration')) {
                        alert(response.message);
                        if (response.redirect) {
                            window.location.href = response.redirect;
                        }
                        return;
                    }

                    const errors = response.errors;
                    // Clear previous errors
                    $('.text-red-600').remove();

                    // Display new errors
                    Object.keys(errors).forEach(function(key) {
                        const input = $(`[name="${key}"]`);
                        input.after(`<p class="mt-1 text-sm text-red-600">${errors[key][0]}</p>`);
                    });
                } else {
                    alert('Error creating product: ' + (xhr.responseJSON?.message || 'Unknown error'));
                }
            }
        });
    });

    // ClickUp Cascading Dropdowns functionality

    // Space selection change handler
    $('#clickup_space_id').change(function() {
        const spaceId = $(this).val();
        const folderSelect = $('#clickup_folder_id');
        const listSelect = $('#clickup_list_id');

        // Reset dependent dropdowns
        folderSelect.empty().append('<option value="">Select Folder (Optional)</option>').prop('disabled', !spaceId);
        listSelect.empty().append('<option value="">Select List *</option>').prop('disabled', true);

        if (spaceId) {
            loadClickUpFolders(spaceId);
            loadClickUpLists(spaceId); // Load folderless lists
        }
    });

    // Folder selection change handler
    $('#clickup_folder_id').change(function() {
        const folderId = $(this).val();
        const spaceId = $('#clickup_space_id').val();
        const listSelect = $('#clickup_list_id');

        // Reset list dropdown
        listSelect.empty().append('<option value="">Select List *</option>');

        if (folderId) {
            loadClickUpListsFromFolder(folderId);
        } else if (spaceId) {
            loadClickUpLists(spaceId); // Load folderless lists
        }
    });

    function loadClickUpSpaces() {
        const spaceSelect = $('#clickup_space_id');

        updateClickUpStatus('loading', 'Loading spaces...');

        $.ajax({
            url: '{{ route("settings.clickup-spaces") }}',
            type: 'GET',
            success: function(response) {
                if (response.success && response.spaces.length > 0) {
                    spaceSelect.empty().append('<option value="">Select Space *</option>');
                    response.spaces.forEach(function(space) {
                        spaceSelect.append(`<option value="${space.id}">${space.name}</option>`);
                    });

                    updateClickUpStatus('success', `${response.spaces.length} spaces loaded`);
                } else {
                    updateClickUpStatus('error', 'No spaces found');
                }
            },
            error: function() {
                updateClickUpStatus('error', 'Error loading spaces');
            }
        });
    }

    function loadClickUpFolders(spaceId) {
        const folderSelect = $('#clickup_folder_id');

        $.ajax({
            url: '{{ route("settings.clickup-folders") }}',
            type: 'GET',
            data: { space_id: spaceId },
            success: function(response) {
                if (response.success) {
                    folderSelect.empty().append('<option value="">Select Folder (Optional)</option>');
                    response.folders.forEach(function(folder) {
                        folderSelect.append(`<option value="${folder.id}">${folder.name}</option>`);
                    });
                    folderSelect.prop('disabled', false);
                }
            },
            error: function() {
                console.error('Error loading folders');
            }
        });
    }

    function loadClickUpLists(spaceId) {
        const listSelect = $('#clickup_list_id');

        $.ajax({
            url: '{{ route("settings.clickup-lists") }}',
            type: 'GET',
            data: { space_id: spaceId },
            success: function(response) {
                if (response.success) {
                    // Clear and add folderless lists
                    listSelect.empty().append('<option value="">Select List *</option>');
                    response.lists.forEach(function(list) {
                        listSelect.append(`<option value="${list.id}">${list.name} (No Folder)</option>`);
                    });
                    listSelect.prop('disabled', false);

                    updateClickUpStatus('success', `${response.lists.length} lists available`);
                }
            },
            error: function() {
                updateClickUpStatus('error', 'Error loading lists');
            }
        });
    }

    function loadClickUpListsFromFolder(folderId) {
        const listSelect = $('#clickup_list_id');

        $.ajax({
            url: '{{ route("settings.clickup-lists") }}',
            type: 'GET',
            data: { folder_id: folderId },
            success: function(response) {
                if (response.success) {
                    listSelect.empty().append('<option value="">Select List *</option>');
                    response.lists.forEach(function(list) {
                        listSelect.append(`<option value="${list.id}">${list.name}</option>`);
                    });
                    listSelect.prop('disabled', false);

                    updateClickUpStatus('success', `${response.lists.length} lists in folder`);
                }
            },
            error: function() {
                updateClickUpStatus('error', 'Error loading folder lists');
            }
        });
    }

    function updateClickUpStatus(type, message) {
        const statusDiv = $('#clickup-status');
        let html = '';

        switch(type) {
            case 'loading':
                html = `<span class="px-3 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                    <i class="fas fa-spinner fa-spin mr-1"></i>${message}
                </span>`;
                break;
            case 'success':
                html = `<span class="px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                    <i class="fas fa-check mr-1"></i>${message}
                </span>`;
                break;
            case 'error':
                html = `<span class="px-3 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                    <i class="fas fa-exclamation-circle mr-1"></i>${message}
                </span>`;
                break;
        }

        statusDiv.html(html);
    }

    // Initialize ClickUp integration on page load
    initializeClickUp();

    function initializeClickUp() {
        loadClickUpSpaces();
    }
});
</script>
@endpush
@endsection
