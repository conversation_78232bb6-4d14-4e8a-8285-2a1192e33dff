@extends('layouts.app')

@section('page-title', 'Edit Product')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="mb-4 sm:mb-6">
        <div class="flex items-center space-x-3 sm:space-x-4">
            <a href="{{ route('products.index') }}" class="text-gray-600 hover:text-gray-800 p-1">
                <i class="fas fa-arrow-left text-lg"></i>
            </a>
            <div>
                <h1 class="text-xl sm:text-2xl font-bold text-gray-900">Edit Product</h1>
                <p class="text-sm sm:text-base text-gray-600">Update product information</p>
            </div>
        </div>
    </div>

    <form id="product-form" action="{{ route('products.update', $product) }}" method="POST" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Product Name *</label>
                    <input type="text" id="name" name="name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('name', $product->name) }}">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="md:col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="description" name="description" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">{{ old('description', $product->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="phase" class="block text-sm font-medium text-gray-700 mb-2">Phase *</label>
                    <select id="phase" name="phase" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">Select Phase</option>
                        <option value="idea" {{ old('phase', $product->phase) === 'idea' ? 'selected' : '' }}>Idea</option>
                        <option value="in_progress" {{ old('phase', $product->phase) === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                        <option value="done" {{ old('phase', $product->phase) === 'done' ? 'selected' : '' }}>Done</option>
                    </select>
                    @error('phase')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Timeline</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                    <input type="date" id="start_date" name="start_date"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('start_date', $product->start_date?->format('Y-m-d')) }}">
                    @error('start_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="target_date" class="block text-sm font-medium text-gray-700 mb-2">Target Date</label>
                    <input type="date" id="target_date" name="target_date"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('target_date', $product->target_date?->format('Y-m-d')) }}">
                    @error('target_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="completion_date" class="block text-sm font-medium text-gray-700 mb-2">Completion Date</label>
                    <input type="date" id="completion_date" name="completion_date"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('completion_date', $product->completion_date?->format('Y-m-d')) }}">
                    @error('completion_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- ClickUp Integration -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-external-link-alt text-gray-600"></i>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">ClickUp Integration</h2>
                        <p class="text-sm text-gray-600">Connect this product to multiple ClickUp lists</p>
                    </div>
                </div>
                <div id="clickup-status">
                    <!-- Status will be populated by JavaScript -->
                </div>
            </div>

            <!-- Current Assignments Display -->
            @if($product->clickupLists->count() > 0)
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-gray-900 mb-3">Current Assignments</h3>
                    <div class="space-y-2">
                        @foreach($product->clickupLists as $assignment)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                                        {{ $assignment->list_type_name }}
                                    </span>
                                    <span class="text-sm text-gray-900">
                                        {{ $assignment->clickup_list_data['name'] ?? 'ClickUp List' }}
                                    </span>
                                </div>
                                <button type="button" class="remove-list-assignment text-red-600 hover:text-red-700 text-sm"
                                        data-assignment-id="{{ $assignment->id }}">
                                    <i class="fas fa-trash mr-1"></i>
                                    Remove
                                </button>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Add New Assignment -->
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6" id="new-assignment-section">
                <h3 class="text-sm font-medium text-gray-900 mb-4">Add New List Assignment</h3>

                <!-- Selection Form -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                    <!-- Space Selection -->
                    <div>
                        <label for="clickup_space_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Space <span class="text-red-500">*</span>
                        </label>
                        <select id="clickup_space_id" name="clickup_space_id"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                            <option value="">Select workspace...</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Your ClickUp workspace</p>
                    </div>

                    <!-- Folder Selection -->
                    <div>
                        <label for="clickup_folder_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Folder <span class="text-gray-400">(Optional)</span>
                        </label>
                        <select id="clickup_folder_id" name="clickup_folder_id" disabled
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm">
                            <option value="">No folder</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Organize lists in folders</p>
                    </div>

                    <!-- List Selection -->
                    <div>
                        <label for="clickup_list_id" class="block text-sm font-medium text-gray-700 mb-2">
                            List <span class="text-red-500">*</span>
                        </label>
                        <select id="clickup_list_id" name="clickup_list_id" disabled
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm">
                            <option value="">Select list...</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Where tasks will be managed</p>
                    </div>

                    <!-- List Type Selection -->
                    <div>
                        <label for="list_type" class="block text-sm font-medium text-gray-700 mb-2">
                            List Type <span class="text-red-500">*</span>
                        </label>
                        <select id="list_type" name="list_type"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                            <option value="">Select type...</option>
                            <option value="features">Features</option>
                            <option value="bugs">Bugs</option>
                            <option value="other">Other</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Purpose of this list</p>
                    </div>
                </div>

                <!-- Add Assignment Button -->
                <div class="mt-4 flex justify-end">
                    <button type="button" id="add-list-assignment"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm font-medium disabled:bg-gray-400 disabled:cursor-not-allowed"
                            disabled>
                        <i class="fas fa-plus mr-2"></i>
                        Add List Assignment
                    </button>
                </div>
            </div>

            <!-- Instructions -->
            <div class="mt-6 p-3 sm:p-4 bg-blue-50 rounded-lg">
                <div class="flex flex-col sm:flex-row">
                    <i class="fas fa-info-circle text-blue-600 mb-2 sm:mb-0 sm:mt-0.5 sm:mr-3"></i>
                    <div>
                        <h4 class="text-sm font-medium text-blue-900 mb-1">Multiple List Integration</h4>
                        <p class="text-sm text-blue-800 leading-relaxed">
                            Assign this product to multiple ClickUp lists for different purposes. For example, use a "Features" list for development tasks,
                            a "Bugs" list for issue tracking, and "Other" for miscellaneous tasks.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-4 sm:p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Team Assignment</h2>

            <div id="team-members-container">
                @if($product->teamMembers->count() > 0)
                    @foreach($product->teamMembers as $assignedMember)
                        <div class="team-member-row grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Team Member</label>
                                <select name="team_members[]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm">
                                    <option value="">Select Team Member</option>
                                    @foreach($teamMembers as $member)
                                        <option value="{{ $member->id }}" {{ $member->id === $assignedMember->id ? 'selected' : '' }}>
                                            {{ $member->name }} ({{ $member->role }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Role in Product</label>
                                <div class="flex flex-col sm:flex-row sm:space-x-2 space-y-2 sm:space-y-0">
                                    <input type="text" name="roles[]" placeholder="e.g., Lead Developer, Designer"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                                           value="{{ $assignedMember->pivot->role_in_product }}">
                                    @if(!$loop->first)
                                        <button type="button" class="remove-team-member text-red-600 hover:text-red-700 px-3 py-2 border border-red-300 rounded-md sm:border-0">
                                            <i class="fas fa-trash mr-1 sm:mr-0"></i>
                                            <span class="sm:hidden">Remove</span>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="team-member-row grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Team Member</label>
                            <select name="team_members[]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm">
                                <option value="">Select Team Member</option>
                                @foreach($teamMembers as $member)
                                    <option value="{{ $member->id }}">{{ $member->name }} ({{ $member->role }})</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role in Product</label>
                            <input type="text" name="roles[]" placeholder="e.g., Lead Developer, Designer"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm">
                        </div>
                    </div>
                @endif
            </div>
            
            <button type="button" id="add-team-member" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                <i class="fas fa-plus mr-1"></i> Add Another Team Member
            </button>
        </div>

        <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4">
            <a href="{{ route('products.index') }}"
               class="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium text-center">
                Cancel
            </a>
            <button type="submit"
                    class="w-full sm:w-auto px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 font-medium">
                <i class="fas fa-save mr-2"></i>
                Update Product
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Add team member functionality
    $('#add-team-member').click(function() {
        const container = $('#team-members-container');
        const newRow = $('.team-member-row:first').clone();
        
        // Clear values
        newRow.find('select').val('');
        newRow.find('input').val('');
        
        // Add remove button if not exists
        if (newRow.find('.remove-team-member').length === 0) {
            const roleDiv = newRow.find('input[name="roles[]"]').parent();
            roleDiv.removeClass('flex space-x-2');
            roleDiv.addClass('flex space-x-2');
            newRow.find('input[name="roles[]"]').addClass('flex-1');
            roleDiv.append(`
                <button type="button" class="remove-team-member text-red-600 hover:text-red-700 px-3 py-2">
                    <i class="fas fa-trash"></i>
                </button>
            `);
        }
        
        container.append(newRow);
    });

    // Remove team member functionality
    $(document).on('click', '.remove-team-member', function() {
        $(this).closest('.team-member-row').remove();
    });

    // Form submission will be handled by the combined handler below

    // ClickUp Cascading Dropdowns functionality
    const currentListId = '{{ $product->clickup_list_id }}';

    // Space selection change handler
    $('#clickup_space_id').change(function() {
        const spaceId = $(this).val();
        const folderSelect = $('#clickup_folder_id');
        const listSelect = $('#clickup_list_id');

        // Reset dependent dropdowns
        folderSelect.empty().append('<option value="">Select Folder (Optional)</option>').prop('disabled', !spaceId);
        listSelect.empty().append('<option value="">Select List *</option>').prop('disabled', true);

        if (spaceId) {
            loadClickUpFolders(spaceId);
            loadClickUpLists(spaceId); // Load folderless lists
        }
    });

    // Folder selection change handler
    $('#clickup_folder_id').change(function() {
        const folderId = $(this).val();
        const spaceId = $('#clickup_space_id').val();
        const listSelect = $('#clickup_list_id');

        // Reset list dropdown
        listSelect.empty().append('<option value="">Select List *</option>');

        if (folderId) {
            loadClickUpListsFromFolder(folderId);
        } else if (spaceId) {
            loadClickUpLists(spaceId); // Load folderless lists
        }
    });

    function loadClickUpSpaces() {
        const spaceSelect = $('#clickup_space_id');

        updateClickUpStatus('loading', 'Loading spaces...');

        $.ajax({
            url: '{{ route("settings.clickup-spaces") }}',
            type: 'GET',
            success: function(response) {
                if (response.success && response.spaces.length > 0) {
                    spaceSelect.empty().append('<option value="">Choose your workspace...</option>');
                    response.spaces.forEach(function(space) {
                        spaceSelect.append(`<option value="${space.id}">${space.name}</option>`);
                    });

                    updateClickUpStatus('success', `${response.spaces.length} spaces loaded`);
                } else {
                    updateClickUpStatus('error', 'No spaces found');
                }
            },
            error: function() {
                updateClickUpStatus('error', 'Error loading spaces');
            }
        });
    }

    function loadClickUpFolders(spaceId) {
        const folderSelect = $('#clickup_folder_id');

        $.ajax({
            url: '{{ route("settings.clickup-folders") }}',
            type: 'GET',
            data: { space_id: spaceId },
            success: function(response) {
                if (response.success) {
                    folderSelect.empty().append('<option value="">Select Folder (Optional)</option>');
                    response.folders.forEach(function(folder) {
                        folderSelect.append(`<option value="${folder.id}">${folder.name}</option>`);
                    });
                    folderSelect.prop('disabled', false);
                }
            },
            error: function() {
                console.error('Error loading folders');
            }
        });
    }

    function loadClickUpLists(spaceId) {
        const listSelect = $('#clickup_list_id');

        $.ajax({
            url: '{{ route("settings.clickup-lists") }}',
            type: 'GET',
            data: { space_id: spaceId },
            success: function(response) {
                if (response.success) {
                    // Clear and add folderless lists
                    listSelect.empty().append('<option value="">Select List *</option>');
                    response.lists.forEach(function(list) {
                        const selected = list.id === currentListId ? 'selected' : '';
                        listSelect.append(`<option value="${list.id}" ${selected}>${list.name} (No Folder)</option>`);
                    });
                    listSelect.prop('disabled', false);

                    updateClickUpStatus('success', `${response.lists.length} lists available`);
                }
            },
            error: function() {
                updateClickUpStatus('error', 'Error loading lists');
            }
        });
    }

    function loadClickUpListsFromFolder(folderId) {
        const listSelect = $('#clickup_list_id');

        $.ajax({
            url: '{{ route("settings.clickup-lists") }}',
            type: 'GET',
            data: { folder_id: folderId },
            success: function(response) {
                if (response.success) {
                    listSelect.empty().append('<option value="">Select List *</option>');
                    response.lists.forEach(function(list) {
                        const selected = list.id === currentListId ? 'selected' : '';
                        listSelect.append(`<option value="${list.id}" ${selected}>${list.name}</option>`);
                    });
                    listSelect.prop('disabled', false);

                    updateClickUpStatus('success', `${response.lists.length} lists in folder`);
                }
            },
            error: function() {
                updateClickUpStatus('error', 'Error loading folder lists');
            }
        });
    }

    function updateClickUpStatus(type, message) {
        const statusDiv = $('#clickup-status');
        let html = '';

        switch(type) {
            case 'loading':
                html = `<span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                    <i class="fas fa-spinner fa-spin mr-1"></i>${message}
                </span>`;
                break;
            case 'success':
                html = `<span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">
                    <i class="fas fa-check mr-1"></i>${message}
                </span>`;
                break;
            case 'error':
                html = `<span class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded">
                    <i class="fas fa-exclamation-circle mr-1"></i>${message}
                </span>`;
                break;
        }

        statusDiv.html(html);
    }

    // Initialize ClickUp integration on page load
    initializeClickUp();

    function initializeClickUp() {
        loadClickUpSpaces();
    }

    // Multiple ClickUp Lists Management

    // Add list assignment functionality
    $('#add-list-assignment').click(function() {
        const spaceId = $('#clickup_space_id').val();
        const listId = $('#clickup_list_id').val();
        const listType = $('#list_type').val();

        if (!listId || !listType) {
            alert('Please select both a ClickUp list and list type before adding the assignment.');
            return;
        }

        // Get list name for display
        const listName = $('#clickup_list_id option:selected').text();

        // Send AJAX request to add assignment
        $.ajax({
            url: '{{ route("products.add-clickup-list", $product) }}',
            type: 'POST',
            data: {
                clickup_list_id: listId,
                list_type: listType,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    // Add new assignment to the display
                    addAssignmentToDisplay(response.assignment);

                    // Reset form
                    resetAssignmentForm();

                    // Show success message
                    showMessage('success', response.message);
                } else {
                    showMessage('error', response.message);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                showMessage('error', response?.message || 'Error adding ClickUp list assignment');
            }
        });
    });

    // Remove list assignment functionality
    $(document).on('click', '.remove-list-assignment', function() {
        const assignmentId = $(this).data('assignment-id');
        const assignmentRow = $(this).closest('.flex');

        if (confirm('Are you sure you want to remove this ClickUp list assignment?')) {
            $.ajax({
                url: '{{ route("products.remove-clickup-list", [$product, "ASSIGNMENT_ID"]) }}'.replace('ASSIGNMENT_ID', assignmentId),
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        assignmentRow.remove();
                        showMessage('success', response.message);

                        // Hide current assignments section if no assignments left
                        if ($('.remove-list-assignment').length === 0) {
                            $('.mb-6:has(.remove-list-assignment)').parent().hide();
                        }
                    } else {
                        showMessage('error', response.message);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    showMessage('error', response?.message || 'Error removing ClickUp list assignment');
                }
            });
        }
    });

    // Enable/disable add button based on form completion
    function checkAddButtonState() {
        const listId = $('#clickup_list_id').val();
        const listType = $('#list_type').val();
        $('#add-list-assignment').prop('disabled', !listId || !listType);
    }

    $('#clickup_list_id, #list_type').change(checkAddButtonState);

    // Helper functions
    function addAssignmentToDisplay(assignment) {
        const currentAssignments = $('.space-y-2');
        if (currentAssignments.length === 0) {
            // Create the assignments section if it doesn't exist
            const assignmentsHtml = `
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-gray-900 mb-3">Current Assignments</h3>
                    <div class="space-y-2">
                        ${createAssignmentRow(assignment)}
                    </div>
                </div>
            `;
            $('#new-assignment-section').before(assignmentsHtml);
        } else {
            currentAssignments.append(createAssignmentRow(assignment));
        }
    }

    function createAssignmentRow(assignment) {
        return `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-3">
                    <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                        ${assignment.list_type_name}
                    </span>
                    <span class="text-sm text-gray-900">
                        ${assignment.list_name}
                    </span>
                </div>
                <button type="button" class="remove-list-assignment text-red-600 hover:text-red-700 text-sm"
                        data-assignment-id="${assignment.id}">
                    <i class="fas fa-trash mr-1"></i>
                    Remove
                </button>
            </div>
        `;
    }

    function resetAssignmentForm() {
        $('#clickup_space_id').val('');
        $('#clickup_folder_id').empty().append('<option value="">No folder</option>').prop('disabled', true);
        $('#clickup_list_id').empty().append('<option value="">Select list...</option>').prop('disabled', true);
        $('#list_type').val('');
        checkAddButtonState();
    }

    function showMessage(type, message) {
        // Create a temporary message element
        const messageClass = type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
        const messageHtml = `
            <div class="fixed top-4 right-4 z-50 px-4 py-2 rounded-md ${messageClass} shadow-lg">
                ${message}
            </div>
        `;
        $('body').append(messageHtml);

        // Remove after 3 seconds
        setTimeout(() => {
            $('.fixed.top-4.right-4').remove();
        }, 3000);
    }

    // Update form submission to work without ClickUp list validation
    $('#product-form').off('submit').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    window.location.href = response.redirect;
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    // Clear previous errors
                    $('.text-red-600').remove();

                    // Display new errors
                    Object.keys(errors).forEach(function(key) {
                        const input = $(`[name="${key}"]`);
                        input.after(`<p class="mt-1 text-sm text-red-600">${errors[key][0]}</p>`);
                    });
                } else {
                    alert('Error updating product');
                }
            }
        });
    });
});
</script>
@endpush
@endsection
