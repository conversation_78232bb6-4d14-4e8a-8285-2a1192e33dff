@extends('layouts.app')

@section('page-title', 'Edit Product')

@section('content')
<div class="max-w-4xl mx-auto">
    <div class="mb-6">
        <div class="flex items-center space-x-4">
            <a href="{{ route('products.index') }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit Product</h1>
                <p class="text-gray-600">Update product information</p>
            </div>
        </div>
    </div>

    <form id="product-form" action="{{ route('products.update', $product) }}" method="POST" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Product Name *</label>
                    <input type="text" id="name" name="name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('name', $product->name) }}">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="md:col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="description" name="description" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">{{ old('description', $product->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="phase" class="block text-sm font-medium text-gray-700 mb-2">Phase *</label>
                    <select id="phase" name="phase" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">Select Phase</option>
                        <option value="idea" {{ old('phase', $product->phase) === 'idea' ? 'selected' : '' }}>Idea</option>
                        <option value="in_progress" {{ old('phase', $product->phase) === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                        <option value="done" {{ old('phase', $product->phase) === 'done' ? 'selected' : '' }}>Done</option>
                    </select>
                    @error('phase')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Timeline</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                    <input type="date" id="start_date" name="start_date"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('start_date', $product->start_date?->format('Y-m-d')) }}">
                    @error('start_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="target_date" class="block text-sm font-medium text-gray-700 mb-2">Target Date</label>
                    <input type="date" id="target_date" name="target_date"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('target_date', $product->target_date?->format('Y-m-d')) }}">
                    @error('target_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="completion_date" class="block text-sm font-medium text-gray-700 mb-2">Completion Date</label>
                    <input type="date" id="completion_date" name="completion_date"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ old('completion_date', $product->completion_date?->format('Y-m-d')) }}">
                    @error('completion_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- ClickUp Integration -->
        <div class="bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 rounded-xl shadow-lg border border-purple-100 p-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <div class="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg">
                        <i class="fas fa-external-link-alt text-white text-lg"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                            ClickUp Integration
                        </h2>
                        <p class="text-sm text-gray-600">Connect your product to ClickUp workspace</p>
                    </div>
                </div>
                <div id="clickup-status" class="transition-all duration-300">
                    <!-- Status will be populated by JavaScript -->
                </div>
            </div>

            <!-- Current Assignment Display -->
            @if($product->clickup_list_id)
            <div class="mb-6 p-4 bg-white/70 backdrop-blur-sm rounded-lg border border-white/50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <i class="fas fa-check-circle text-green-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">Currently Connected</p>
                            <p class="text-sm text-gray-600">
                                @if(isset($product->clickup_list_data['name']))
                                    {{ $product->clickup_list_data['name'] }}
                                @else
                                    List ID: {{ $product->clickup_list_id }}
                                @endif
                            </p>
                        </div>
                    </div>
                    <span class="px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                        <i class="fas fa-link mr-1"></i>
                        Connected
                    </span>
                </div>
            </div>
            @endif

            <!-- Hierarchical Selection -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Space Selection -->
                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <div class="p-1.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-md">
                            <i class="fas fa-globe text-white text-sm"></i>
                        </div>
                        <label for="clickup_space_id" class="text-sm font-semibold text-gray-700">
                            1. Select Space *
                        </label>
                    </div>
                    <select id="clickup_space_id" name="clickup_space_id" required
                            class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200 bg-white/80 backdrop-blur-sm">
                        <option value="">Choose your workspace...</option>
                    </select>
                    <p class="text-xs text-gray-500 flex items-center">
                        <i class="fas fa-info-circle mr-1 text-blue-500"></i>
                        Your ClickUp workspace
                    </p>
                </div>

                <!-- Folder Selection -->
                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <div class="p-1.5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-md">
                            <i class="fas fa-folder text-white text-sm"></i>
                        </div>
                        <label for="clickup_folder_id" class="text-sm font-semibold text-gray-700">
                            2. Select Folder
                        </label>
                        <span class="text-xs text-gray-500">(Optional)</span>
                    </div>
                    <select id="clickup_folder_id" name="clickup_folder_id" disabled
                            class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200 bg-white/80 backdrop-blur-sm disabled:bg-gray-100 disabled:cursor-not-allowed">
                        <option value="">Select Folder (Optional)</option>
                    </select>
                    <p class="text-xs text-gray-500 flex items-center">
                        <i class="fas fa-info-circle mr-1 text-purple-500"></i>
                        Organize your lists in folders
                    </p>
                </div>

                <!-- List Selection -->
                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <div class="p-1.5 bg-gradient-to-r from-pink-500 to-red-500 rounded-md">
                            <i class="fas fa-list text-white text-sm"></i>
                        </div>
                        <label for="clickup_list_id" class="text-sm font-semibold text-gray-700">
                            3. Select List *
                        </label>
                    </div>
                    <select id="clickup_list_id" name="clickup_list_id" required disabled
                            class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200 bg-white/80 backdrop-blur-sm disabled:bg-gray-100 disabled:cursor-not-allowed">
                        <option value="">Select List *</option>
                        @if($product->clickup_list_id)
                            <option value="{{ $product->clickup_list_id }}" selected>
                                {{ $product->clickup_list_data['name'] ?? 'Current List' }}
                            </option>
                        @endif
                    </select>
                    <p class="text-xs text-gray-500 flex items-center">
                        <i class="fas fa-info-circle mr-1 text-pink-500"></i>
                        Where your product tasks will be managed
                    </p>
                    @error('clickup_list_id')
                        <p class="mt-1 text-sm text-red-600 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1"></i>
                            {{ $message }}
                        </p>
                    @enderror
                </div>
            </div>

            <!-- Helper Information -->
            <div class="mt-6 p-4 bg-white/50 backdrop-blur-sm rounded-lg border border-white/50">
                <div class="flex items-start space-x-3">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <i class="fas fa-lightbulb text-blue-600"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 mb-1">How it works</h4>
                        <p class="text-sm text-gray-600 leading-relaxed">
                            Select your ClickUp <strong>Space</strong> first, then choose a <strong>Folder</strong> (optional),
                            and finally pick the <strong>List</strong> where this product's tasks will be managed.
                            This connection enables automatic task synchronization and project tracking.
                        </p>
                    </div>
                </div>
            </div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Team Assignment</h2>
            
            <div id="team-members-container">
                @if($product->teamMembers->count() > 0)
                    @foreach($product->teamMembers as $assignedMember)
                        <div class="team-member-row grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Team Member</label>
                                <select name="team_members[]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                    <option value="">Select Team Member</option>
                                    @foreach($teamMembers as $member)
                                        <option value="{{ $member->id }}" {{ $member->id === $assignedMember->id ? 'selected' : '' }}>
                                            {{ $member->name }} ({{ $member->role }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Role in Product</label>
                                <div class="flex space-x-2">
                                    <input type="text" name="roles[]" placeholder="e.g., Lead Developer, Designer"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                           value="{{ $assignedMember->pivot->role_in_product }}">
                                    @if(!$loop->first)
                                        <button type="button" class="remove-team-member text-red-600 hover:text-red-700 px-3 py-2">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="team-member-row grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Team Member</label>
                            <select name="team_members[]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <option value="">Select Team Member</option>
                                @foreach($teamMembers as $member)
                                    <option value="{{ $member->id }}">{{ $member->name }} ({{ $member->role }})</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role in Product</label>
                            <input type="text" name="roles[]" placeholder="e.g., Lead Developer, Designer"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        </div>
                    </div>
                @endif
            </div>
            
            <button type="button" id="add-team-member" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                <i class="fas fa-plus mr-1"></i> Add Another Team Member
            </button>
        </div>

        <div class="flex justify-end space-x-4">
            <a href="{{ route('products.index') }}" 
               class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                Cancel
            </a>
            <button type="submit" 
                    class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 font-medium">
                <i class="fas fa-save mr-2"></i>
                Update Product
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Add team member functionality
    $('#add-team-member').click(function() {
        const container = $('#team-members-container');
        const newRow = $('.team-member-row:first').clone();
        
        // Clear values
        newRow.find('select').val('');
        newRow.find('input').val('');
        
        // Add remove button if not exists
        if (newRow.find('.remove-team-member').length === 0) {
            const roleDiv = newRow.find('input[name="roles[]"]').parent();
            roleDiv.removeClass('flex space-x-2');
            roleDiv.addClass('flex space-x-2');
            newRow.find('input[name="roles[]"]').addClass('flex-1');
            roleDiv.append(`
                <button type="button" class="remove-team-member text-red-600 hover:text-red-700 px-3 py-2">
                    <i class="fas fa-trash"></i>
                </button>
            `);
        }
        
        container.append(newRow);
    });

    // Remove team member functionality
    $(document).on('click', '.remove-team-member', function() {
        $(this).closest('.team-member-row').remove();
    });

    // Form submission will be handled by the combined handler below

    // ClickUp Cascading Dropdowns functionality
    const currentListId = '{{ $product->clickup_list_id }}';

    // Space selection change handler
    $('#clickup_space_id').change(function() {
        const spaceId = $(this).val();
        const folderSelect = $('#clickup_folder_id');
        const listSelect = $('#clickup_list_id');

        // Reset dependent dropdowns
        folderSelect.empty().append('<option value="">Select Folder (Optional)</option>').prop('disabled', !spaceId);
        listSelect.empty().append('<option value="">Select List *</option>').prop('disabled', true);

        if (spaceId) {
            loadClickUpFolders(spaceId);
            loadClickUpLists(spaceId); // Load folderless lists
        }
    });

    // Folder selection change handler
    $('#clickup_folder_id').change(function() {
        const folderId = $(this).val();
        const spaceId = $('#clickup_space_id').val();
        const listSelect = $('#clickup_list_id');

        // Reset list dropdown
        listSelect.empty().append('<option value="">Select List *</option>');

        if (folderId) {
            loadClickUpListsFromFolder(folderId);
        } else if (spaceId) {
            loadClickUpLists(spaceId); // Load folderless lists
        }
    });

    function loadClickUpSpaces() {
        const spaceSelect = $('#clickup_space_id');

        updateClickUpStatus('loading', 'Loading spaces...');

        $.ajax({
            url: '{{ route("settings.clickup-spaces") }}',
            type: 'GET',
            success: function(response) {
                if (response.success && response.spaces.length > 0) {
                    spaceSelect.empty().append('<option value="">Choose your workspace...</option>');
                    response.spaces.forEach(function(space) {
                        spaceSelect.append(`<option value="${space.id}">${space.name}</option>`);
                    });

                    updateClickUpStatus('success', `${response.spaces.length} spaces loaded`);
                } else {
                    updateClickUpStatus('error', 'No spaces found');
                }
            },
            error: function() {
                updateClickUpStatus('error', 'Error loading spaces');
            }
        });
    }

    function loadClickUpFolders(spaceId) {
        const folderSelect = $('#clickup_folder_id');

        $.ajax({
            url: '{{ route("settings.clickup-folders") }}',
            type: 'GET',
            data: { space_id: spaceId },
            success: function(response) {
                if (response.success) {
                    folderSelect.empty().append('<option value="">Select Folder (Optional)</option>');
                    response.folders.forEach(function(folder) {
                        folderSelect.append(`<option value="${folder.id}">${folder.name}</option>`);
                    });
                    folderSelect.prop('disabled', false);
                }
            },
            error: function() {
                console.error('Error loading folders');
            }
        });
    }

    function loadClickUpLists(spaceId) {
        const listSelect = $('#clickup_list_id');

        $.ajax({
            url: '{{ route("settings.clickup-lists") }}',
            type: 'GET',
            data: { space_id: spaceId },
            success: function(response) {
                if (response.success) {
                    // Clear and add folderless lists
                    listSelect.empty().append('<option value="">Select List *</option>');
                    response.lists.forEach(function(list) {
                        const selected = list.id === currentListId ? 'selected' : '';
                        listSelect.append(`<option value="${list.id}" ${selected}>${list.name} (No Folder)</option>`);
                    });
                    listSelect.prop('disabled', false);

                    updateClickUpStatus('success', `${response.lists.length} lists available`);
                }
            },
            error: function() {
                updateClickUpStatus('error', 'Error loading lists');
            }
        });
    }

    function loadClickUpListsFromFolder(folderId) {
        const listSelect = $('#clickup_list_id');

        $.ajax({
            url: '{{ route("settings.clickup-lists") }}',
            type: 'GET',
            data: { folder_id: folderId },
            success: function(response) {
                if (response.success) {
                    listSelect.empty().append('<option value="">Select List *</option>');
                    response.lists.forEach(function(list) {
                        const selected = list.id === currentListId ? 'selected' : '';
                        listSelect.append(`<option value="${list.id}" ${selected}>${list.name}</option>`);
                    });
                    listSelect.prop('disabled', false);

                    updateClickUpStatus('success', `${response.lists.length} lists in folder`);
                }
            },
            error: function() {
                updateClickUpStatus('error', 'Error loading folder lists');
            }
        });
    }

    function updateClickUpStatus(type, message) {
        const statusDiv = $('#clickup-status');
        let html = '';

        switch(type) {
            case 'loading':
                html = `<span class="px-3 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                    <i class="fas fa-spinner fa-spin mr-1"></i>${message}
                </span>`;
                break;
            case 'success':
                html = `<span class="px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                    <i class="fas fa-check mr-1"></i>${message}
                </span>`;
                break;
            case 'error':
                html = `<span class="px-3 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                    <i class="fas fa-exclamation-circle mr-1"></i>${message}
                </span>`;
                break;
        }

        statusDiv.html(html);
    }

    // Initialize ClickUp integration on page load
    initializeClickUp();

    function initializeClickUp() {
        loadClickUpSpaces();
    }

    // Additional form validation for ClickUp list (integrated with existing form handler)
    const originalFormHandler = $('#product-form').data('events')?.submit;
    $('#product-form').off('submit').on('submit', function(e) {
        const clickupListId = $('#clickup_list_id').val();
        if (!clickupListId) {
            e.preventDefault();
            alert('Please select a ClickUp list before updating the product. This is required for project management integration.');
            $('#clickup_list_id').focus().addClass('border-red-500');
            return false;
        }

        // Continue with the original AJAX form submission
        e.preventDefault();

        const formData = new FormData(this);

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    window.location.href = response.redirect;
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    // Clear previous errors
                    $('.text-red-600').remove();

                    // Display new errors
                    Object.keys(errors).forEach(function(key) {
                        const input = $(`[name="${key}"]`);
                        input.after(`<p class="mt-1 text-sm text-red-600">${errors[key][0]}</p>`);
                    });
                } else {
                    alert('Error updating product');
                }
            }
        });
    });
});
</script>
@endpush
@endsection
