@extends('layouts.app')

@section('page-title', $product->name)

@push('styles')
<style>
    .product-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
    }

    .product-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    /* Custom animations for ClickUp Integration */
    @keyframes spin-slow {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .animate-spin-slow {
        animation: spin-slow 3s linear infinite;
    }

    .product-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
    }

    .product-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        border-color: #cbd5e0;
    }

    .stat-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: left 0.6s;
    }

    .stat-card:hover::before {
        left: 100%;
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px -5px rgba(0, 0, 0, 0.15);
    }

    .progress-ring {
        transition: stroke-dasharray 0.5s ease-in-out;
    }

    .floating-actions {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        z-index: 50;
    }

    .floating-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
    }

    .floating-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    }

    .timeline-item {
        position: relative;
        padding-left: 2rem;
    }

    .timeline-item::before {
        content: '';
        position: absolute;
        left: 0.5rem;
        top: 0;
        bottom: -1rem;
        width: 2px;
        background: linear-gradient(to bottom, #e2e8f0, transparent);
    }

    .timeline-item:last-child::before {
        display: none;
    }

    .timeline-dot {
        position: absolute;
        left: 0;
        top: 0.5rem;
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .phase-indicator {
        position: relative;
        overflow: hidden;
    }

    .phase-indicator::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .team-avatar {
        transition: all 0.3s ease;
    }

    .team-avatar:hover {
        transform: scale(1.2);
        z-index: 10;
    }
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <div class="product-hero relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="relative z-10">
                <!-- Navigation -->
                <div class="flex items-center mb-8">
                    <a href="{{ route('products.index') }}"
                       class="text-white hover:text-purple-200 p-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-300">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </a>
                    <nav class="ml-4 text-purple-200 text-sm">
                        <a href="{{ route('products.index') }}" class="hover:text-white">Products</a>
                        <span class="mx-2">/</span>
                        <span class="text-white">{{ $product->name }}</span>
                    </nav>
                </div>

                <!-- Product Header -->
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-start space-x-6 mb-6 lg:mb-0">
                        <!-- Product Icon -->
                        <div class="w-20 h-20 rounded-2xl bg-white bg-opacity-20 backdrop-blur-sm flex items-center justify-center text-white text-2xl font-bold border border-white border-opacity-30">
                            @if($product->hasCustomIcon())
                                <img src="{{ $product->icon_url }}" alt="{{ $product->name }}" class="w-full h-full object-cover rounded-2xl">
                            @else
                                {{ substr($product->name, 0, 2) }}
                            @endif
                        </div>

                        <!-- Product Info -->
                        <div>
                            <h1 class="text-4xl font-bold text-white mb-3">{{ $product->name }}</h1>
                            <div class="flex flex-wrap items-center gap-3 mb-4">
                                <span class="phase-indicator px-4 py-2 text-sm font-medium rounded-full bg-white bg-opacity-20 backdrop-blur-sm text-white border border-white border-opacity-30">
                                    @switch($product->phase)
                                        @case('idea')
                                            💡 Idea Phase
                                            @break
                                        @case('in_progress')
                                            ⚙️ In Development
                                            @break
                                        @case('done')
                                            ✅ Completed
                                            @break
                                    @endswitch
                                </span>

                                @if($product->is_overdue)
                                    <span class="px-4 py-2 text-sm font-medium rounded-full bg-red-500 bg-opacity-90 text-white">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                        Overdue
                                    </span>
                                @endif

                                @if($product->clickup_task_id)
                                    <span class="px-4 py-2 text-sm font-medium rounded-full bg-green-500 bg-opacity-90 text-white">
                                        <i class="fas fa-link mr-1"></i>
                                        ClickUp Synced
                                    </span>
                                @endif
                            </div>

                            @if($product->description)
                                <p class="text-purple-100 text-lg leading-relaxed max-w-2xl">
                                    {{ Str::limit($product->description, 150) }}
                                </p>
                            @endif
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-3">
                        <a href="{{ route('products.edit', $product) }}"
                           class="bg-white bg-opacity-20 backdrop-blur-sm hover:bg-opacity-30 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 border border-white border-opacity-30 flex items-center justify-center">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Product
                        </a>

                        <a href="{{ route('products.documents.index', $product) }}"
                           class="bg-white text-purple-600 hover:bg-purple-50 px-6 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center">
                            <i class="fas fa-folder-open mr-2"></i>
                            Documents ({{ $product->currentDocuments->count() }})
                        </a>

                        @if($product->clickup_task_id && isset($product->clickup_data['url']))
                            <a href="{{ $product->clickup_data['url'] }}" target="_blank"
                               class="bg-purple-500 bg-opacity-20 backdrop-blur-sm hover:bg-opacity-30 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 border border-white border-opacity-30 flex items-center justify-center">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                ClickUp
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-16 relative z-20 mb-12">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <!-- Documents Count -->
            <div class="stat-card rounded-2xl p-6 text-center">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white text-xl font-bold mx-auto mb-3">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="text-2xl font-bold text-gray-900 mb-1">{{ $product->currentDocuments->count() }}</div>
                <div class="text-sm text-gray-600">Documents</div>
            </div>

            <!-- Team Members -->
            <div class="stat-card rounded-2xl p-6 text-center">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center text-white text-xl font-bold mx-auto mb-3">
                    <i class="fas fa-users"></i>
                </div>
                <div class="text-2xl font-bold text-gray-900 mb-1">{{ $product->teamMembers->count() }}</div>
                <div class="text-sm text-gray-600">Team Members</div>
            </div>

            <!-- Days Active -->
            <div class="stat-card rounded-2xl p-6 text-center">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center text-white text-xl font-bold mx-auto mb-3">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="text-2xl font-bold text-gray-900 mb-1">
                    @if($product->start_date)
                        {{ $product->start_date->diffInDays(now()) }}
                    @else
                        0
                    @endif
                </div>
                <div class="text-sm text-gray-600">Days Active</div>
            </div>

            <!-- Progress -->
            <div class="stat-card rounded-2xl p-6 text-center">
                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center text-white text-xl font-bold mx-auto mb-3">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="text-2xl font-bold text-gray-900 mb-1">
                    @switch($product->phase)
                        @case('idea') 25% @break
                        @case('in_progress') 65% @break
                        @case('done') 100% @break
                        @default 0%
                    @endswitch
                </div>
                <div class="text-sm text-gray-600">Progress</div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Column -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Product Overview -->
                <div class="product-card rounded-2xl p-8">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold mr-3">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Product Overview</h2>
                    </div>

                    @if($product->description)
                        <div class="prose prose-lg max-w-none">
                            <p class="text-gray-700 leading-relaxed text-lg">{{ $product->description }}</p>
                        </div>
                    @else
                        <div class="text-center py-12">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-file-alt text-gray-400 text-xl"></i>
                            </div>
                            <p class="text-gray-500 text-lg">No description provided yet</p>
                            <a href="{{ route('products.edit', $product) }}" class="text-blue-600 hover:text-blue-700 font-medium mt-2 inline-block">
                                Add description →
                            </a>
                        </div>
                    @endif
                </div>

                <!-- Project Timeline -->
                <div class="product-card rounded-2xl p-8">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg flex items-center justify-center text-white font-bold mr-3">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Project Timeline</h2>
                    </div>

                    <div class="space-y-6">
                        @if($product->created_at)
                            <div class="timeline-item">
                                <div class="timeline-dot bg-blue-500"></div>
                                <div class="bg-blue-50 rounded-xl p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <h3 class="font-semibold text-blue-900">Project Created</h3>
                                        <span class="text-sm text-blue-600">{{ $product->created_at->format('M d, Y') }}</span>
                                    </div>
                                    <p class="text-blue-700 text-sm">Initial project setup and planning phase</p>
                                </div>
                            </div>
                        @endif

                        @if($product->start_date)
                            <div class="timeline-item">
                                <div class="timeline-dot bg-green-500"></div>
                                <div class="bg-green-50 rounded-xl p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <h3 class="font-semibold text-green-900">Development Started</h3>
                                        <span class="text-sm text-green-600">{{ $product->start_date->format('M d, Y') }}</span>
                                    </div>
                                    <p class="text-green-700 text-sm">Active development and implementation phase</p>
                                    <div class="mt-2 text-xs text-green-600">
                                        {{ $product->start_date->diffInDays(now()) }} days ago
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($product->target_date)
                            <div class="timeline-item">
                                <div class="timeline-dot {{ $product->is_overdue ? 'bg-red-500' : ($product->phase === 'done' ? 'bg-green-500' : 'bg-orange-500') }}"></div>
                                <div class="bg-{{ $product->is_overdue ? 'red' : ($product->phase === 'done' ? 'green' : 'orange') }}-50 rounded-xl p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <h3 class="font-semibold text-{{ $product->is_overdue ? 'red' : ($product->phase === 'done' ? 'green' : 'orange') }}-900">
                                            @if($product->phase === 'done')
                                                Project Completed
                                            @else
                                                Target Completion
                                            @endif
                                        </h3>
                                        <span class="text-sm text-{{ $product->is_overdue ? 'red' : ($product->phase === 'done' ? 'green' : 'orange') }}-600">
                                            {{ $product->target_date->format('M d, Y') }}
                                        </span>
                                    </div>
                                    <p class="text-{{ $product->is_overdue ? 'red' : ($product->phase === 'done' ? 'green' : 'orange') }}-700 text-sm">
                                        @if($product->phase === 'done')
                                            Successfully delivered on schedule
                                        @elseif($product->is_overdue)
                                            Project is overdue and needs attention
                                        @else
                                            Planned completion date
                                        @endif
                                    </p>
                                    @if($product->is_overdue && $product->phase !== 'done')
                                        <div class="mt-2 text-xs text-red-600 font-medium">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>
                                            Overdue by {{ $product->target_date->diffInDays(now()) }} days
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif

                        @if($product->completion_date)
                            <div class="timeline-item">
                                <div class="timeline-dot bg-green-500"></div>
                                <div class="bg-green-50 rounded-xl p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <h3 class="font-semibold text-green-900">Project Completed</h3>
                                        <span class="text-sm text-green-600">{{ $product->completion_date->format('M d, Y') }}</span>
                                    </div>
                                    <p class="text-green-700 text-sm">Project successfully delivered and completed</p>
                                </div>
                            </div>
                        @endif

                        @if(!$product->start_date && !$product->target_date && !$product->completion_date)
                            <div class="text-center py-8">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-calendar-alt text-gray-400 text-xl"></i>
                                </div>
                                <p class="text-gray-500 text-lg mb-2">No timeline information available</p>
                                <a href="{{ route('products.edit', $product) }}" class="text-blue-600 hover:text-blue-700 font-medium">
                                    Set project dates →
                                </a>
                            </div>
                        @endif
                    </div>
                </div>

            <!-- Enhanced ClickUp Integration -->
            <div class="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden transition-all duration-500 hover:shadow-2xl hover:border-gray-200 group">
                <!-- Header with Gradient Background -->
                <div class="bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 p-6 sm:p-8 relative overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
                    <div class="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -translate-y-16 translate-x-16"></div>
                    <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>

                    <div class="relative z-10">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                            <div class="flex items-center group/header">
                                <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center text-white mr-4 flex-shrink-0 shadow-lg transform transition-all duration-300 group-hover/header:scale-110 group-hover/header:rotate-3">
                                    <i class="fas fa-external-link-alt text-lg"></i>
                                </div>
                                <div>
                                    <h2 class="text-2xl sm:text-3xl font-bold text-white mb-1 transition-all duration-300">ClickUp Integration</h2>
                                    <p class="text-sm text-white/80">Synchronized project management & task tracking</p>
                                </div>
                            </div>

                            @if($product->clickupLists->count() > 0)
                                <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
                                    <div class="bg-white/20 backdrop-blur-sm rounded-2xl px-4 py-3 border border-white/30">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-emerald-500 rounded-xl flex items-center justify-center shadow-lg">
                                                <i class="fas fa-check-circle text-white text-sm"></i>
                                            </div>
                                            <div>
                                                <div class="text-white font-bold text-lg">{{ $product->clickupLists->count() }}</div>
                                                <div class="text-white/80 text-xs">List{{ $product->clickupLists->count() !== 1 ? 's' : '' }} Connected</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-3 py-2">
                                        <div class="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                                        <span class="text-xs text-white/90 font-medium">Live Sync Active</span>
                                    </div>
                                </div>
                            @else
                                <div class="bg-white/20 backdrop-blur-sm rounded-2xl px-4 py-3 border border-white/30">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gray-500 rounded-xl flex items-center justify-center shadow-lg">
                                            <i class="fas fa-times-circle text-white text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-bold">No Lists</div>
                                            <div class="text-white/80 text-xs">Not Connected</div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                @if($product->clickupLists->count() > 0)
                    <!-- Content Area -->
                    <div class="p-6 sm:p-8">
                        <!-- Section Header -->
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 space-y-4 sm:space-y-0">
                            <div class="flex items-center group/title">
                                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center text-white mr-4 shadow-lg transform transition-all duration-300 group-hover/title:scale-110">
                                    <i class="fas fa-list-ul text-sm"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-1">List Assignments</h3>
                                    <p class="text-sm text-gray-500">Organized by task categories</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl px-4 py-3 border border-blue-100">
                                <div class="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-sync-alt text-white text-xs animate-spin-slow"></i>
                                </div>
                                <span class="text-sm font-medium text-blue-700">Auto-synced</span>
                            </div>
                        </div>

                        @php
                            $groupedLists = $product->clickupLists->groupBy('list_type');
                        @endphp

                        <!-- Category Grid -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
                            @foreach(['features', 'bugs', 'other'] as $type)
                                @if(isset($groupedLists[$type]) && $groupedLists[$type]->count() > 0)
                                    <div class="group bg-gradient-to-br from-white to-gray-50 rounded-3xl border border-gray-200 shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden hover:border-gray-300 transform hover:-translate-y-1">
                                        <!-- Category Header -->
                                        <div class="relative overflow-hidden">
                                            @switch($type)
                                                @case('features')
                                                    <div class="bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 p-6 relative">
                                                        <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
                                                        <div class="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
                                                        <div class="relative z-10 flex items-center justify-between">
                                                            <div class="flex items-center">
                                                                <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center text-white mr-4 shadow-lg">
                                                                    <i class="fas fa-lightbulb text-lg"></i>
                                                                </div>
                                                                <div>
                                                                    <h4 class="text-lg font-bold text-white">Features</h4>
                                                                    <p class="text-sm text-white/80">Development tasks</p>
                                                                </div>
                                                            </div>
                                                            <div class="bg-white/20 backdrop-blur-sm rounded-2xl px-4 py-2 border border-white/30">
                                                                <span class="text-white font-bold text-lg">{{ $groupedLists[$type]->count() }}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @break
                                                @case('bugs')
                                                    <div class="bg-gradient-to-br from-red-500 via-rose-500 to-pink-600 p-6 relative">
                                                        <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
                                                        <div class="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
                                                        <div class="relative z-10 flex items-center justify-between">
                                                            <div class="flex items-center">
                                                                <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center text-white mr-4 shadow-lg">
                                                                    <i class="fas fa-bug text-lg"></i>
                                                                </div>
                                                                <div>
                                                                    <h4 class="text-lg font-bold text-white">Bug Fixes</h4>
                                                                    <p class="text-sm text-white/80">Issue tracking</p>
                                                                </div>
                                                            </div>
                                                            <div class="bg-white/20 backdrop-blur-sm rounded-2xl px-4 py-2 border border-white/30">
                                                                <span class="text-white font-bold text-lg">{{ $groupedLists[$type]->count() }}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @break
                                                @case('other')
                                                    <div class="bg-gradient-to-br from-gray-600 via-slate-600 to-zinc-700 p-6 relative">
                                                        <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
                                                        <div class="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
                                                        <div class="relative z-10 flex items-center justify-between">
                                                            <div class="flex items-center">
                                                                <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center text-white mr-4 shadow-lg">
                                                                    <i class="fas fa-tasks text-lg"></i>
                                                                </div>
                                                                <div>
                                                                    <h4 class="text-lg font-bold text-white">Other Tasks</h4>
                                                                    <p class="text-sm text-white/80">General items</p>
                                                                </div>
                                                            </div>
                                                            <div class="bg-white/20 backdrop-blur-sm rounded-2xl px-4 py-2 border border-white/30">
                                                                <span class="text-white font-bold text-lg">{{ $groupedLists[$type]->count() }}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @break
                                            @endswitch
                                        </div>

                                        <!-- List Items -->
                                        <div class="p-6 space-y-4">
                                            @foreach($groupedLists[$type] as $assignment)
                                                <div class="group/item bg-white rounded-2xl border border-gray-200 hover:border-gray-300 hover:shadow-lg transition-all duration-300 overflow-hidden transform hover:-translate-y-0.5">
                                                    <div class="p-5">
                                                        <div class="flex items-start justify-between">
                                                            <div class="flex-1 min-w-0">
                                                                <!-- List Name with Enhanced Link -->
                                                                <div class="flex items-center space-x-3 mb-4">
                                                                    @if(isset($assignment->clickup_list_data['name']))
                                                                        <a href="https://app.clickup.com/{{ $assignment->clickup_list_id }}"
                                                                           target="_blank"
                                                                           class="text-base font-bold text-gray-900 hover:text-blue-600 transition-colors duration-200 truncate group-hover/item:underline flex items-center space-x-2">
                                                                            <span>{{ $assignment->clickup_list_data['name'] }}</span>
                                                                            <i class="fas fa-external-link-alt text-xs text-gray-400 group-hover/item:text-blue-500 transition-colors duration-200"></i>
                                                                        </a>
                                                                    @else
                                                                        <span class="text-base font-bold text-gray-900">ClickUp List</span>
                                                                    @endif
                                                                </div>

                                                                <!-- Enhanced Hierarchical Path -->
                                                                <div class="flex items-center flex-wrap gap-2 mb-4">
                                                                    @if(isset($assignment->clickup_list_data['space']))
                                                                        <div class="inline-flex items-center px-3 py-2 text-xs font-semibold bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 rounded-xl border border-blue-200">
                                                                            <i class="fas fa-layer-group mr-2 text-xs"></i>
                                                                            {{ $assignment->clickup_list_data['space'] }}
                                                                        </div>
                                                                        <i class="fas fa-chevron-right text-xs text-gray-400"></i>
                                                                    @endif
                                                                    @if(isset($assignment->clickup_list_data['folder']))
                                                                        <div class="inline-flex items-center px-3 py-2 text-xs font-semibold bg-gradient-to-r from-purple-50 to-pink-50 text-purple-700 rounded-xl border border-purple-200">
                                                                            <i class="fas fa-folder mr-2 text-xs"></i>
                                                                            {{ $assignment->clickup_list_data['folder'] }}
                                                                        </div>
                                                                    @else
                                                                        <div class="inline-flex items-center px-3 py-2 text-xs font-semibold bg-gradient-to-r from-gray-50 to-slate-50 text-gray-600 rounded-xl border border-gray-200">
                                                                            <i class="fas fa-folder-open mr-2 text-xs"></i>
                                                                            No Folder
                                                                        </div>
                                                                    @endif
                                                                    <i class="fas fa-chevron-right text-xs text-gray-400"></i>
                                                                    <div class="inline-flex items-center px-3 py-2 text-xs font-semibold bg-gradient-to-r from-emerald-50 to-green-50 text-emerald-700 rounded-xl border border-emerald-200">
                                                                        <i class="fas fa-list mr-2 text-xs"></i>
                                                                        {{ $assignment->clickup_list_data['name'] ?? 'List' }}
                                                                    </div>
                                                                </div>

                                                                <!-- Assignment Date with Enhanced Styling -->
                                                                <div class="flex items-center space-x-3 text-xs">
                                                                    <div class="flex items-center bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl px-3 py-2 border border-gray-200">
                                                                        <i class="fas fa-calendar-plus mr-2 text-gray-500"></i>
                                                                        <span class="font-medium text-gray-700">{{ $assignment->created_at->format('M d, Y') }}</span>
                                                                    </div>
                                                                    <div class="w-1 h-1 bg-gray-300 rounded-full"></div>
                                                                    <span class="text-gray-500 font-medium">{{ $assignment->created_at->diffForHumans() }}</span>
                                                                </div>
                                                            </div>

                                                            <!-- Enhanced Status Indicator -->
                                                            <div class="flex flex-col items-end space-y-3 ml-6">
                                                                <div class="flex items-center space-x-2">
                                                                    <div class="w-3 h-3 bg-emerald-400 rounded-full animate-pulse shadow-lg"></div>
                                                                    <span class="text-xs font-medium text-emerald-600">Active</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>

                @else
                    <!-- Enhanced No Lists Connected State -->
                    <div class="p-8 sm:p-12">
                        <div class="text-center py-16 bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 rounded-3xl border-2 border-dashed border-gray-300 relative overflow-hidden">
                            <!-- Background Pattern -->
                            <div class="absolute inset-0 bg-gradient-to-br from-white/50 to-transparent"></div>
                            <div class="absolute top-0 right-0 w-32 h-32 bg-blue-100/30 rounded-full -translate-y-16 translate-x-16"></div>
                            <div class="absolute bottom-0 left-0 w-24 h-24 bg-purple-100/30 rounded-full translate-y-12 -translate-x-12"></div>

                            <div class="relative z-10">
                                <div class="w-24 h-24 bg-gradient-to-br from-purple-500 via-indigo-500 to-blue-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl transform transition-transform duration-300 hover:scale-110">
                                    <i class="fas fa-link text-white text-3xl"></i>
                                </div>
                                <h3 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">No ClickUp Lists Connected</h3>
                                <p class="text-gray-600 mb-10 max-w-2xl mx-auto leading-relaxed text-lg">
                                    Connect this product to ClickUp lists to unlock powerful task tracking, progress monitoring, and comprehensive project insights with real-time synchronization.
                                </p>
                                <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
                                    <a href="{{ route('products.edit', $product) }}"
                                       class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 text-white rounded-2xl hover:from-purple-700 hover:via-indigo-700 hover:to-blue-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 font-semibold text-lg">
                                        <i class="fas fa-plus mr-3"></i>
                                        Connect ClickUp Lists
                                    </a>
                                    <div class="flex items-center bg-white rounded-2xl px-6 py-4 shadow-lg border border-gray-200">
                                        <i class="fas fa-info-circle mr-3 text-blue-500"></i>
                                        <span class="text-gray-700 font-medium">Supports Features, Bugs & Other tasks</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

            <!-- Right Sidebar -->
            <div class="space-y-8">
                <!-- Team Members -->
                <div class="product-card rounded-2xl p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg flex items-center justify-center text-white font-bold mr-3">
                                <i class="fas fa-users"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-900">Team</h2>
                        </div>
                        <span class="px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">
                            {{ $product->teamMembers->count() }} member{{ $product->teamMembers->count() !== 1 ? 's' : '' }}
                        </span>
                    </div>

                    @if($product->teamMembers->count() > 0)
                        <div class="space-y-4">
                            @foreach($product->teamMembers as $member)
                                <div class="flex items-center space-x-4 p-3 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors">
                                    <div class="team-avatar w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-lg">
                                        {{ substr($member->name, 0, 1) }}
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="font-semibold text-gray-900">{{ $member->name }}</h3>
                                        <p class="text-sm text-gray-600">{{ $member->role }}</p>
                                        @if($member->pivot && $member->pivot->role_in_product)
                                            <p class="text-xs text-blue-600">{{ $member->pivot->role_in_product }}</p>
                                        @endif
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        @if($member->is_active)
                                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                            <span class="text-xs text-green-600">Active</span>
                                        @else
                                            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                            <span class="text-xs text-gray-500">Inactive</span>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <div class="mt-6 pt-4 border-t border-gray-200">
                            <a href="{{ route('products.edit', $product) }}"
                               class="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center">
                                <i class="fas fa-user-plus mr-2"></i>
                                Manage Team
                            </a>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-users text-gray-400 text-xl"></i>
                            </div>
                            <p class="text-gray-500 text-lg mb-4">No team members assigned</p>
                            <a href="{{ route('products.edit', $product) }}"
                               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i class="fas fa-user-plus mr-2"></i>
                                Add Team Members
                            </a>
                        </div>
                    @endif
                </div>

            <!-- Quick Stats -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h2>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Team Size</span>
                        <span class="font-medium">{{ $product->teamMembers->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Created</span>
                        <span class="font-medium">{{ $product->created_at->format('M d, Y') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Last Updated</span>
                        <span class="font-medium">{{ $product->updated_at->format('M d, Y') }}</span>
                    </div>
                    @if($product->start_date && $product->target_date)
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Duration</span>
                            <span class="font-medium">
                                {{ $product->start_date->diffInDays($product->target_date) }} days
                            </span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Actions</h2>
                <div class="space-y-2">
                    <a href="{{ route('products.edit', $product) }}" 
                       class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Product
                    </a>
                    <button onclick="deleteProduct()" 
                            class="w-full flex items-center justify-center px-4 py-2 border border-red-300 text-red-700 rounded-md hover:bg-red-50 transition-colors">
                        <i class="fas fa-trash mr-2"></i>
                        Delete Product
                    </button>
                </div>
            </div>

            <!-- Product Documents -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-file-alt mr-2 text-blue-600"></i>
                        Documents
                    </h2>
                    <div class="flex space-x-2">
                        <a href="{{ route('products.documents.create', $product) }}"
                           class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                            <i class="fas fa-plus mr-1"></i>
                            Upload Document
                        </a>
                        <a href="{{ route('products.documents.index', $product) }}"
                           class="text-gray-600 hover:text-gray-700 text-sm font-medium">
                            <i class="fas fa-folder-open mr-1"></i>
                            View All
                        </a>
                    </div>
                </div>

                @if($product->currentDocuments->count() > 0)
                    <div class="space-y-3">
                        @foreach($product->currentDocuments->take(5) as $document)
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    @php
                                        $fileIcon = match(strtolower($document->file_type)) {
                                            'pdf' => 'pdf',
                                            'doc', 'docx' => 'word',
                                            'xls', 'xlsx' => 'excel',
                                            'ppt', 'pptx' => 'powerpoint',
                                            'txt', 'md' => 'alt',
                                            default => 'alt'
                                        };
                                    @endphp
                                    <i class="fas fa-file-{{ $fileIcon }} text-xl text-gray-400"></i>
                                    <div>
                                        <div class="flex items-center space-x-2">
                                            <h4 class="font-medium text-gray-900">{{ $document->title }}</h4>
                                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">
                                                v{{ $document->version }}
                                            </span>
                                        </div>
                                        <div class="text-sm text-gray-600">
                                            <span>{{ $document->file_size_human }}</span>
                                            <span class="mx-2">•</span>
                                            <span>{{ $document->uploaded_at->format('M d, Y') }}</span>
                                            @if($document->uploader)
                                                <span class="mx-2">•</span>
                                                <span>{{ $document->uploader->name }}</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="{{ $document->download_url }}"
                                       class="text-primary-600 hover:text-primary-700 p-1" title="Download">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <a href="{{ route('products.documents.show', [$product, $document]) }}"
                                       class="text-gray-600 hover:text-gray-700 p-1" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        @endforeach

                        @if($product->currentDocuments->count() > 5)
                            <div class="text-center pt-2">
                                <a href="{{ route('products.documents.index', $product) }}"
                                   class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                                    View {{ $product->currentDocuments->count() - 5 }} more documents
                                </a>
                            </div>
                        @endif
                    </div>
                @else
                    <div class="text-center py-6">
                        <i class="fas fa-file-alt text-3xl text-gray-300 mb-3"></i>
                        <p class="text-gray-500 mb-3">No documents uploaded yet</p>
                        <a href="{{ route('products.documents.create', $product) }}"
                           class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                            <i class="fas fa-plus mr-1"></i>
                            Upload First Document
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Floating Action Buttons -->
    <div class="floating-actions">
        <div class="flex flex-col space-y-3">
            <a href="{{ route('products.documents.create', $product) }}"
               class="floating-btn w-14 h-14 rounded-full flex items-center justify-center text-white shadow-lg hover:shadow-xl transition-all duration-300"
               title="Add Document">
                <i class="fas fa-plus text-xl"></i>
            </a>

            <a href="{{ route('products.edit', $product) }}"
               class="w-14 h-14 rounded-full flex items-center justify-center text-white shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-yellow-500 to-orange-600"
               title="Edit Product">
                <i class="fas fa-edit text-xl"></i>
            </a>

            @if($product->clickup_task_id && isset($product->clickup_data['url']))
                <a href="{{ $product->clickup_data['url'] }}" target="_blank"
                   class="w-14 h-14 rounded-full flex items-center justify-center text-white shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-purple-500 to-pink-600"
                   title="Open in ClickUp">
                    <i class="fas fa-external-link-alt text-xl"></i>
                </a>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
function deleteProduct() {
    if (!confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
        return;
    }

    $.ajax({
        url: '{{ route("products.destroy", $product) }}',
        type: 'DELETE',
        success: function(response) {
            if (response.success) {
                window.location.href = '{{ route("products.index") }}';
            }
        },
        error: function() {
            alert('Error deleting product');
        }
    });
}

// Product data for email generation
window.productData = {
    name: @json($product->name),
    phase: @json(ucfirst($product->phase)),
    start_date: @json($product->start_date ? $product->start_date->format('M d, Y') : null),
    target_date: @json($product->target_date ? $product->target_date->format('M d, Y') : null),
    description: @json($product->description)
};

function syncWithClickUp() {
    if (!confirm('This will create or update the ClickUp task for this product. Continue?')) {
        return;
    }

    $.ajax({
        url: '{{ route("products.sync-clickup", $product) }}',
        type: 'POST',
        success: function(response) {
            if (response.success) {
                alert(response.message);
                location.reload();
            } else {
                alert('Sync failed: ' + response.message);
            }
        },
        error: function() {
            alert('Error syncing with ClickUp');
        }
    });
}

function refreshClickUpData() {
    $.ajax({
        url: '{{ route("products.refresh-clickup", $product) }}',
        type: 'POST',
        success: function(response) {
            if (response.success) {
                alert(response.message);
                location.reload();
            } else {
                alert('Refresh failed: ' + response.message);
            }
        },
        error: function() {
            alert('Error refreshing ClickUp data');
        }
    });
}

function loadProgressData() {
    const startDate = document.getElementById('progress-start-date').value;
    const endDate = document.getElementById('progress-end-date').value;
    const listFilter = document.getElementById('list-filter').value;

    if (!startDate || !endDate) {
        alert('Please select both start and end dates');
        return;
    }

    // Show loading state
    const container = document.getElementById('progress-data-container');
    container.innerHTML = `
        <div class="text-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading progress data...</p>
        </div>
    `;

    // Prepare request data
    const requestData = {
        start_date: startDate,
        end_date: endDate
    };

    // Add list type filter if specified
    if (listFilter) {
        requestData.list_type_filter = listFilter;
    }

    $.ajax({
        url: '{{ route("products.clickup-progress", $product) }}',
        type: 'GET',
        data: requestData,
        success: function(response) {
            if (response.success) {
                displayProgressData(response.data, response.date_range);
            } else {
                container.innerHTML = `
                    <div class="text-center py-8 text-red-600">
                        <i class="fas fa-exclamation-triangle text-3xl mb-3"></i>
                        <p>Error loading progress data: ${response.message}</p>
                    </div>
                `;
            }
        },
        error: function(xhr) {
            container.innerHTML = `
                <div class="text-center py-8 text-red-600">
                    <i class="fas fa-exclamation-triangle text-3xl mb-3"></i>
                    <p>Error loading progress data. Please try again.</p>
                </div>
            `;
        }
    });
}

function displayProgressData(progressData, dateRange) {
    const container = document.getElementById('progress-data-container');

    if (!progressData || progressData.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-chart-line text-3xl mb-3"></i>
                <p>No task data found for the selected date range</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="space-y-6">
            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
    `;

    // Calculate overall totals
    let totalTasks = 0;
    let totalCompleted = 0;
    let totalInProgress = 0;
    let totalOpen = 0;

    progressData.forEach(list => {
        totalTasks += list.progress.total_tasks;
        totalCompleted += list.progress.completed_tasks;
        totalInProgress += list.progress.in_progress_tasks;
        totalOpen += list.progress.open_tasks;
    });

    const overallCompletionRate = totalTasks > 0 ? ((totalCompleted / totalTasks) * 100).toFixed(1) : 0;

    html += `
                <div class="bg-blue-50 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-blue-900">Total Tasks</p>
                            <p class="text-2xl font-bold text-blue-600">${totalTasks}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-green-50 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-green-900">Completed</p>
                            <p class="text-2xl font-bold text-green-600">${totalCompleted}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-yellow-900">In Progress</p>
                            <p class="text-2xl font-bold text-yellow-600">${totalInProgress}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-purple-50 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-purple-900">Completion Rate</p>
                            <p class="text-2xl font-bold text-purple-600">${overallCompletionRate}%</p>
                        </div>
                    </div>
                </div>
            </div>
    `;

    // Individual list progress
    html += `
            <!-- Individual List Progress -->
            <div class="space-y-4">
                <h4 class="text-lg font-semibold text-gray-900">Progress by List</h4>
    `;

    progressData.forEach(list => {
        const progress = list.progress;
        const completionRate = progress.total_tasks > 0 ? ((progress.completed_tasks / progress.total_tasks) * 100).toFixed(1) : 0;

        html += `
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 bg-${list.list_type === 'features' ? 'blue' : (list.list_type === 'bugs' ? 'red' : 'gray')}-500 rounded-lg flex items-center justify-center text-white text-sm">
                                <i class="fas fa-${list.list_type === 'features' ? 'lightbulb' : (list.list_type === 'bugs' ? 'bug' : 'tasks')}"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center space-x-2">
                                    <h5 class="font-medium text-gray-900">${list.list_name}</h5>
                                    <span class="px-2 py-1 text-xs font-medium bg-${list.list_type === 'features' ? 'blue' : (list.list_type === 'bugs' ? 'red' : 'gray')}-100 text-${list.list_type === 'features' ? 'blue' : (list.list_type === 'bugs' ? 'red' : 'gray')}-800 rounded">
                                        ${list.list_type_name}
                                    </span>
                                </div>
                                <div class="mt-1 text-sm text-gray-600">
                                    <i class="fas fa-sitemap mr-1 text-gray-400"></i>
                                    <span class="hierarchical-path font-mono text-xs bg-gray-50 px-2 py-1 rounded border">${list.hierarchical_path || 'ClickUp List'}</span>
                                </div>
                            </div>
                        </div>
                        <span class="px-3 py-1 text-sm font-medium bg-gray-100 text-gray-800 rounded-full">
                            ${completionRate}% Complete
                        </span>
                    </div>

                    <!-- Progress Bar -->
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                        <div class="bg-green-500 h-2 rounded-full" style="width: ${completionRate}%"></div>
                    </div>

                    <!-- Task Statistics -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div class="text-center">
                            <p class="font-medium text-gray-900">${progress.total_tasks}</p>
                            <p class="text-gray-600">Total</p>
                        </div>
                        <div class="text-center">
                            <p class="font-medium text-green-600">${progress.completed_tasks}</p>
                            <p class="text-gray-600">Completed</p>
                        </div>
                        <div class="text-center">
                            <p class="font-medium text-yellow-600">${progress.in_progress_tasks}</p>
                            <p class="text-gray-600">In Progress</p>
                        </div>
                        <div class="text-center">
                            <p class="font-medium text-gray-600">${progress.open_tasks}</p>
                            <p class="text-gray-600">Open</p>
                        </div>
                    </div>

                    ${progress.avg_completion_time_days > 0 ? `
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <p class="text-sm text-gray-600">
                            <i class="fas fa-clock mr-1"></i>
                            Average completion time: <span class="font-medium">${progress.avg_completion_time_days} days</span>
                        </p>
                    </div>
                    ` : ''}
                </div>
        `;
    });

    html += `
            </div>
        </div>
    `;

    container.innerHTML = html;
}

function refreshProgressData() {
    loadProgressData();
}

// Email Modal Functions
let selectedRecipients = [];

function openEmailModal() {
    console.log('Opening email modal...');

    // Set default date range to current progress filter dates
    const startDateElement = document.getElementById('progress-start-date');
    const endDateElement = document.getElementById('progress-end-date');
    const listFilterElement = document.getElementById('list-filter');

    console.log('Progress elements found:', {
        startDate: !!startDateElement,
        endDate: !!endDateElement,
        listFilter: !!listFilterElement
    });

    // Get values with null checks
    const startDate = startDateElement ? startDateElement.value : '';
    const endDate = endDateElement ? endDateElement.value : '';
    const listFilter = listFilterElement ? listFilterElement.value : '';

    // Set email modal values with null checks
    const emailStartDateElement = document.getElementById('emailStartDate');
    const emailEndDateElement = document.getElementById('emailEndDate');
    const emailListFilterElement = document.getElementById('emailListFilter');

    if (emailStartDateElement) emailStartDateElement.value = startDate;
    if (emailEndDateElement) emailEndDateElement.value = endDate;
    if (emailListFilterElement) emailListFilterElement.value = listFilter;

    const emailModal = document.getElementById('emailModal');
    if (emailModal) {
        emailModal.classList.remove('hidden');
    } else {
        alert('Email modal not found. Please refresh the page and try again.');
    }
}

function closeEmailModal() {
    document.getElementById('emailModal').classList.add('hidden');
    selectedRecipients = [];
    updateRecipientsDisplay();
    document.getElementById('emailReportForm').reset();
}

function addRecipient(email, name = null) {
    if (!selectedRecipients.find(r => r.email === email)) {
        selectedRecipients.push({ email, name });
        updateRecipientsDisplay();
    }
}

function addEmailFromInput() {
    const emailInput = document.getElementById('emailInput');
    const email = emailInput.value.trim();

    if (email && isValidEmail(email)) {
        addRecipient(email);
        emailInput.value = '';
    } else {
        alert('Please enter a valid email address.');
    }
}

function removeRecipient(email) {
    selectedRecipients = selectedRecipients.filter(r => r.email !== email);
    updateRecipientsDisplay();
}

function updateRecipientsDisplay() {
    const container = document.getElementById('selectedRecipients');
    container.innerHTML = '';

    selectedRecipients.forEach(recipient => {
        const div = document.createElement('div');
        div.className = 'flex items-center justify-between bg-gray-100 px-3 py-2 rounded-md';
        div.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-envelope mr-2 text-gray-500"></i>
                <span class="text-sm">${recipient.name ? `${recipient.name} (${recipient.email})` : recipient.email}</span>
            </div>
            <button type="button" onclick="removeRecipient('${recipient.email}')"
                    class="text-red-500 hover:text-red-700">
                <i class="fas fa-times"></i>
            </button>
        `;
        container.appendChild(div);
    });
}

function isValidEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

function sendEmailReport() {
    if (selectedRecipients.length === 0) {
        alert('Please select at least one recipient.');
        return;
    }

    const startDateElement = document.getElementById('emailStartDate');
    const endDateElement = document.getElementById('emailEndDate');

    if (!startDateElement || !endDateElement) {
        alert('Email form elements not found. Please refresh the page and try again.');
        return;
    }

    const startDate = startDateElement.value;
    const endDate = endDateElement.value;

    if (!startDate || !endDate) {
        alert('Please select both start and end dates.');
        return;
    }

    const sendBtn = document.getElementById('sendEmailBtn');
    const originalText = sendBtn.innerHTML;

    // Show loading state
    sendBtn.disabled = true;
    sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generating...';

    try {
        // Generate email content
        generateEmailReport(startDate, endDate, () => {
            // Reset button state
            sendBtn.disabled = false;
            sendBtn.innerHTML = originalText;
        });
    } catch (error) {
        console.error('Error generating email report:', error);
        alert('An error occurred while generating the email report.');
        sendBtn.disabled = false;
        sendBtn.innerHTML = originalText;
    }
}

function generateEmailReport(startDate, endDate, callback) {
    const emailListFilterElement = document.getElementById('emailListFilter');
    const listFilter = emailListFilterElement ? emailListFilterElement.value : '';

    // Fetch progress data
    const params = new URLSearchParams({
        start_date: startDate,
        end_date: endDate
    });

    if (listFilter) {
        params.append('list_type_filter', listFilter);
    }

    fetch(`{{ route('products.clickup-progress', $product) }}?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createMailtoLink(data.data, startDate, endDate);
            } else {
                alert('Error fetching progress data: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while fetching progress data.');
        })
        .finally(() => {
            if (callback) callback();
        });
}

function createMailtoLink(progressData, startDate, endDate) {
    const customMessageElement = document.getElementById('customMessage');
    const customMessage = customMessageElement ? customMessageElement.value : '';

    // Format dates for display
    const startDateFormatted = new Date(startDate).toLocaleDateString('en-US', {
        year: 'numeric', month: 'short', day: 'numeric'
    });
    const endDateFormatted = new Date(endDate).toLocaleDateString('en-US', {
        year: 'numeric', month: 'short', day: 'numeric'
    });

    // Create subject using global product data
    const subject = `ClickUp Progress Report for ${window.productData.name} - ${startDateFormatted} to ${endDateFormatted}`;

    // Create HTML email body
    const htmlEmailBody = createEmailBody(progressData, startDate, endDate, customMessage);

    // Create recipients list
    const recipients = selectedRecipients.map(r => r.email).join(',');

    // Check if mailto URL would be too long (HTML emails are larger, so use conservative limit)
    const testMailtoUrl = `mailto:${recipients}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(htmlEmailBody)}`;

    if (testMailtoUrl.length > 1500) {
        // Show copy to clipboard option instead
        showCopyToClipboardOption(recipients, subject, htmlEmailBody);
    } else {
        // Create and trigger mailto link
        const mailtoUrl = testMailtoUrl;
        window.location.href = mailtoUrl;

        // Show success message and close modal
        setTimeout(() => {
            alert('Email client opened with pre-filled report. Please review and send the email.');
            closeEmailModal();
        }, 500);
    }
}

function createEmailBody(progressData, startDate, endDate, customMessage) {
    const startDateFormatted = new Date(startDate).toLocaleDateString('en-US', {
        year: 'numeric', month: 'long', day: 'numeric'
    });
    const endDateFormatted = new Date(endDate).toLocaleDateString('en-US', {
        year: 'numeric', month: 'long', day: 'numeric'
    });

    // Get product data from global variables
    const productName = window.productData.name;
    const productPhase = window.productData.phase;
    const productStartDate = window.productData.start_date;
    const productTargetDate = window.productData.target_date;
    const productDescription = window.productData.description;

    // Generate HTML email body
    return createHtmlEmailBody(progressData, startDate, endDate, customMessage, {
        productName,
        productPhase,
        productStartDate,
        productTargetDate,
        productDescription,
        startDateFormatted,
        endDateFormatted
    });
}

function createHtmlEmailBody(progressData, startDate, endDate, customMessage, productData) {
    const generatedDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric', month: 'long', day: 'numeric',
        hour: 'numeric', minute: '2-digit'
    });

    // Calculate overall statistics
    let totalTasks = 0;
    let totalCompleted = 0;
    let totalInProgress = 0;
    let totalOpen = 0;
    let totalCompletionTime = 0;
    let listsWithCompletionTime = 0;

    progressData.forEach(list => {
        const progress = list.progress;
        totalTasks += progress.total_tasks;
        totalCompleted += progress.completed_tasks;
        totalInProgress += progress.in_progress_tasks;
        totalOpen += progress.open_tasks;

        if (progress.avg_completion_time_days > 0) {
            totalCompletionTime += progress.avg_completion_time_days;
            listsWithCompletionTime++;
        }
    });

    const overallCompletionRate = totalTasks > 0 ? Math.round((totalCompleted / totalTasks) * 100) : 0;
    const avgCompletionTime = listsWithCompletionTime > 0 ? Math.round(totalCompletionTime / listsWithCompletionTime * 10) / 10 : 0;

    // Start building Outlook-compatible HTML email
    let html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClickUp Progress Report - ${productData.productName}</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Outlook-compatible styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
        }
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f8fafc;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 8px;
            max-width: 800px;
            margin: 0 auto;
        }
        .header-table {
            background-color: #667eea;
            width: 100%;
        }
        .header-cell {
            padding: 30px;
            text-align: center;
            color: #ffffff;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e2e8f0;
        }
        .card {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }
        .stats-table {
            width: 100%;
            margin-bottom: 20px;
            border-collapse: separate;
            border-spacing: 10px;
        }
        .stat-cell {
            background-color: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            vertical-align: top;
            width: 14.28%;
        }
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
            display: block;
        }
        .stat-label {
            font-size: 12px;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 5px;
        }
        .progress-bar {
            background: #e2e8f0;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        .badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .badge-features { background: #e6fffa; color: #234e52; }
        .badge-bugs { background: #fed7d7; color: #742a2a; }
        .badge-other { background: #e2e8f0; color: #4a5568; }
        .list-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .list-header-table {
            width: 100%;
            margin-bottom: 15px;
        }
        .list-header-left {
            text-align: left;
            vertical-align: middle;
        }
        .list-header-right {
            text-align: right;
            vertical-align: middle;
        }
        .list-name {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
        }
        .hierarchical-path {
            font-size: 12px;
            color: #718096;
            margin-top: 5px;
        }
        .task-stats-table {
            width: 100%;
            margin-top: 15px;
            border-collapse: separate;
            border-spacing: 5px;
        }
        .task-stat-cell {
            text-align: center;
            padding: 8px;
            background-color: #f7fafc;
            border-radius: 6px;
            width: 25%;
        }
        .task-stat-number {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
        }
        .task-stat-label {
            font-size: 10px;
            color: #718096;
            text-transform: uppercase;
        }
        .footer {
            background: #f7fafc;
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
            color: #718096;
            font-size: 12px;
        }
        .completion-rate-high { color: #38a169; }
        .completion-rate-medium { color: #d69e2e; }
        .completion-rate-low { color: #e53e3e; }
        @media (max-width: 600px) {
            .stat-cell {
                width: 50%;
                display: inline-block;
                vertical-align: top;
            }
            .task-stat-cell {
                width: 50%;
                display: inline-block;
                vertical-align: top;
            }
            .content-table { padding: 15px; }
            .card-content { padding: 15px; }
        }
    </style>
</head>
<body>
    <table class="email-container" cellpadding="0" cellspacing="0" border="0">
        <tr>
            <td>
                <table class="header-table" cellpadding="0" cellspacing="0" border="0">
                    <tr>
                        <td class="header-cell">
                            <h1 class="header-title">📊 ClickUp Progress Report</h1>
                            <p class="header-subtitle">${productData.productName} • ${productData.startDateFormatted} - ${productData.endDateFormatted}</p>
                        </td>
                    </tr>
                </table>

                <table class="content-table" cellpadding="0" cellspacing="0" border="0">
                    <tr>
                        <td>`;

    // Add custom message if provided
    if (customMessage) {
        html += `
                            <table class="section-table" cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                    <td>
                                        <table class="card-table" cellpadding="0" cellspacing="0" border="0">
                                            <tr>
                                                <td class="card-content">
                                                    <h3 style="margin: 0 0 10px 0; color: #2d3748;">📝 Message</h3>
                                                    <p style="margin: 0; color: #4a5568;">${customMessage}</p>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>`;
    }

    // Product Overview Section
    html += `
                            <table class="section-table" cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                    <td>
                                        <h2 class="section-title">📋 Product Overview</h2>
                                        <table class="card-table" cellpadding="0" cellspacing="0" border="0">
                                            <tr>
                                                <td class="card-content">
                                                    <table cellpadding="0" cellspacing="0" border="0" style="width: 100%;">
                                                        <tr>
                                                            <td style="width: 50%; vertical-align: top; padding-right: 10px;">
                                                                <strong style="color: #2d3748;">Product:</strong><br>
                                                                <span style="color: #4a5568;">${productData.productName}</span>
                                                            </td>
                                                            <td style="width: 50%; vertical-align: top; padding-left: 10px;">
                                                                <strong style="color: #2d3748;">Phase:</strong><br>
                                                                <span style="color: #4a5568;">${productData.productPhase}</span>
                                                            </td>
                                                        </tr>`;

    if (productData.productStartDate || productData.productTargetDate) {
        html += `
                                                        <tr>`;
        if (productData.productStartDate) {
            html += `
                                                            <td style="width: 50%; vertical-align: top; padding-right: 10px; padding-top: 15px;">
                                                                <strong style="color: #2d3748;">Start Date:</strong><br>
                                                                <span style="color: #4a5568;">${productData.productStartDate}</span>
                                                            </td>`;
        } else {
            html += `<td style="width: 50%;"></td>`;
        }

        if (productData.productTargetDate) {
            html += `
                                                            <td style="width: 50%; vertical-align: top; padding-left: 10px; padding-top: 15px;">
                                                                <strong style="color: #2d3748;">Target Date:</strong><br>
                                                                <span style="color: #4a5568;">${productData.productTargetDate}</span>
                                                            </td>`;
        } else {
            html += `<td style="width: 50%;"></td>`;
        }

        html += `
                                                        </tr>`;
    }

    html += `
                                                    </table>`;

    if (productData.productDescription) {
        html += `
                                                    <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e2e8f0;">
                                                        <strong style="color: #2d3748;">Description:</strong><br>
                                                        <span style="color: #4a5568;">${productData.productDescription}</span>
                                                    </div>`;
    }

    html += `
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>

            <!-- Executive Summary -->
            <div class="section">
                <h2 class="section-title">📈 Executive Summary</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number ${overallCompletionRate >= 80 ? 'completion-rate-high' : overallCompletionRate >= 50 ? 'completion-rate-medium' : 'completion-rate-low'}">${overallCompletionRate}%</span>
                        <div class="stat-label">Completion Rate</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">${totalTasks}</span>
                        <div class="stat-label">Total Tasks</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number completion-rate-high">${totalCompleted}</span>
                        <div class="stat-label">Completed</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number completion-rate-medium">${totalInProgress}</span>
                        <div class="stat-label">In Progress</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">${totalOpen}</span>
                        <div class="stat-label">Open Tasks</div>
                    </div>`;

    if (avgCompletionTime > 0) {
        html += `
                    <div class="stat-card">
                        <span class="stat-number">${avgCompletionTime}</span>
                        <div class="stat-label">Avg Days to Complete</div>
                    </div>`;
    }

    html += `
                    <div class="stat-card">
                        <span class="stat-number">${progressData.length}</span>
                        <div class="stat-label">Connected Lists</div>
                    </div>
                </div>
            </div>`;

    // Progress by List Section
    html += `
            <div class="section">
                <h2 class="section-title">📝 Progress by ClickUp List</h2>`;

    if (progressData.length === 0) {
        html += `
                <div class="card">
                    <p style="text-align: center; color: #718096; margin: 20px 0;">
                        No ClickUp lists are currently assigned to this product.
                    </p>
                </div>`;
    } else {
        progressData.forEach((list, index) => {
            const progress = list.progress;
            const completionRate = progress.total_tasks > 0 ? Math.round((progress.completed_tasks / progress.total_tasks) * 100) : 0;

            // Determine list type badge class
            let badgeClass = 'badge-other';
            if (list.list_type_name.toLowerCase().includes('feature')) badgeClass = 'badge-features';
            else if (list.list_type_name.toLowerCase().includes('bug')) badgeClass = 'badge-bugs';

            // Determine progress bar color
            let progressColor = '#e53e3e'; // Red for low completion
            if (completionRate >= 80) progressColor = '#38a169'; // Green for high completion
            else if (completionRate >= 50) progressColor = '#d69e2e'; // Yellow for medium completion

            html += `
                <div class="list-card">
                    <div class="list-header">
                        <div>
                            <div class="list-name">${list.list_name}</div>
                            <div class="hierarchical-path">🗂️ ${list.hierarchical_path}</div>
                        </div>
                        <span class="badge ${badgeClass}">${list.list_type_name}</span>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                            <span style="font-weight: 600; color: #2d3748;">Progress</span>
                            <span style="font-weight: 600; color: ${progressColor};">${completionRate}%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${completionRate}%; background-color: ${progressColor};"></div>
                        </div>
                    </div>

                    <div class="task-stats">
                        <div class="task-stat">
                            <div class="task-stat-number">${progress.total_tasks}</div>
                            <div class="task-stat-label">Total</div>
                        </div>
                        <div class="task-stat">
                            <div class="task-stat-number" style="color: #38a169;">${progress.completed_tasks}</div>
                            <div class="task-stat-label">Completed</div>
                        </div>
                        <div class="task-stat">
                            <div class="task-stat-number" style="color: #d69e2e;">${progress.in_progress_tasks}</div>
                            <div class="task-stat-label">In Progress</div>
                        </div>
                        <div class="task-stat">
                            <div class="task-stat-number" style="color: #718096;">${progress.open_tasks}</div>
                            <div class="task-stat-label">Open</div>
                        </div>`;

            if (progress.avg_completion_time_days > 0) {
                html += `
                        <div class="task-stat">
                            <div class="task-stat-number">${progress.avg_completion_time_days}</div>
                            <div class="task-stat-label">Avg Days</div>
                        </div>`;
            }

            html += `
                    </div>
                </div>`;
        });
    }

    html += `
            </div>
        </div>

        <div class="footer">
            <p style="margin: 0;">
                📊 This report was generated by the Product Management System on ${generatedDate}<br>
                For more detailed information, please access the product dashboard.
            </p>
        </div>
    </div>
</body>
</html>`;

    return html;
}

function showCopyToClipboardOption(recipients, subject, emailBody) {
    // Create a modal or alert with copy to clipboard functionality
    const copyModal = document.createElement('div');
    copyModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    copyModal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-96 overflow-y-auto">
            <h3 class="text-lg font-semibold mb-4">📧 HTML Email Report Ready</h3>
            <p class="text-gray-600 mb-4">The email report has been generated with professional HTML formatting. Copy the content below and paste it into your email client. Most modern email clients support HTML formatting.</p>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">Recipients:</label>
                <input type="text" value="${recipients}" readonly class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm">
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">Subject:</label>
                <input type="text" value="${subject}" readonly class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm">
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">HTML Email Body:</label>
                <div class="border border-gray-300 rounded-md bg-gray-50 p-3 max-h-48 overflow-y-auto">
                    <div class="text-xs text-gray-500 mb-2">Preview (copy button will copy the full HTML):</div>
                    <div class="bg-white border rounded p-2 text-xs" style="max-height: 200px; overflow-y: auto;">
                        ${emailBody}
                    </div>
                </div>
                <textarea readonly class="hidden w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm h-32" id="emailBodyText">${emailBody}</textarea>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-500 mt-1 mr-2"></i>
                    <div class="text-sm text-blue-700">
                        <strong>How to use:</strong> Copy the content below, then paste it into your email client. If your email client supports HTML (like Outlook, Gmail, Apple Mail), the formatting will be preserved. If not, the content will display as formatted text.
                    </div>
                </div>
            </div>

            <div class="flex space-x-3">
                <button onclick="copyEmailToClipboard()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-copy mr-2"></i>Copy HTML Email to Clipboard
                </button>
                <button onclick="closeCopyModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                    Close
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(copyModal);

    // Store data for copying
    window.emailCopyData = { recipients, subject, emailBody };
}

function copyEmailToClipboard() {
    const data = window.emailCopyData;
    const fullEmailContent = `To: ${data.recipients}\nSubject: ${data.subject}\n\n${data.emailBody}`;

    navigator.clipboard.writeText(fullEmailContent).then(() => {
        alert('Email content copied to clipboard! You can now paste it into your email client.');
        closeCopyModal();
        closeEmailModal();
    }).catch(err => {
        console.error('Failed to copy to clipboard:', err);
        // Fallback: select the text
        const textarea = document.getElementById('emailBodyText');
        textarea.select();
        document.execCommand('copy');
        alert('Email content copied to clipboard! You can now paste it into your email client.');
        closeCopyModal();
        closeEmailModal();
    });
}

function closeCopyModal() {
    const modal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-50');
    if (modal) {
        modal.remove();
    }
    delete window.emailCopyData;
}

// Initialize date inputs with current month
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    // Set progress date inputs with null checks
    const startDateElement = document.getElementById('progress-start-date');
    const endDateElement = document.getElementById('progress-end-date');

    if (startDateElement) startDateElement.value = firstDay.toISOString().split('T')[0];
    if (endDateElement) endDateElement.value = lastDay.toISOString().split('T')[0];

    // Load initial data
    loadProgressData();
});
</script>
@endpush

<!-- Enhanced Email Insights Report Modal -->
<div id="emailModal" class="fixed inset-0 bg-black bg-opacity-60 overflow-y-auto h-full w-full hidden z-50 backdrop-blur-sm">
    <div class="relative top-4 sm:top-8 lg:top-16 mx-auto p-4 sm:p-6 w-11/12 sm:w-10/12 md:w-3/4 lg:w-2/3 xl:w-1/2 max-w-4xl">
        <div class="bg-white rounded-2xl shadow-2xl border border-gray-200 max-h-[90vh] overflow-hidden">
            <div class="flex flex-col h-full">
                <!-- Enhanced Modal Header -->
                <div class="flex items-center justify-between p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-100">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center text-white shadow-lg">
                            <i class="fas fa-envelope text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg sm:text-xl font-bold text-gray-900">Generate Email Report</h3>
                            <p class="text-sm text-green-700">Professional ClickUp progress insights</p>
                        </div>
                    </div>
                    <button onclick="closeEmailModal()"
                            class="w-8 h-8 bg-white rounded-lg flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-50 transition-all duration-200 shadow-sm">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- Enhanced Modal Description -->
                <div class="px-6 py-4 bg-blue-50 border-b border-blue-100">
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center text-white flex-shrink-0 mt-0.5">
                            <i class="fas fa-info text-xs"></i>
                        </div>
                        <div>
                            <p class="text-sm text-blue-900 font-medium mb-1">How it works</p>
                            <p class="text-sm text-blue-700">
                                This will open your default email client with a professionally formatted progress report.
                                You can review, customize, and send it using your preferred email application.
                            </p>
                        </div>
                    </div>
                </div>

            <!-- Modal Body -->
            <div class="py-4">
                <form id="emailReportForm">
                    <!-- Recipients Section -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Email Recipients <span class="text-red-500">*</span>
                        </label>

                        <!-- Team Members Quick Select -->
                        @php
                            $teamMembersWithEmail = $product->teamMembers->filter(function($member) {
                                return !empty($member->email);
                            });
                        @endphp
                        @if($teamMembersWithEmail->count() > 0)
                        <div class="mb-3">
                            <p class="text-sm text-gray-600 mb-2">Quick select team members:</p>
                            <div class="flex flex-wrap gap-2">
                                @foreach($teamMembersWithEmail as $member)
                                    <button type="button" onclick="addRecipient('{{ $member->email }}', '{{ addslashes($member->name) }}')"
                                            class="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors">
                                        <i class="fas fa-user mr-1"></i>
                                        {{ $member->name }}
                                    </button>
                                @endforeach
                            </div>
                        </div>
                        @elseif($product->teamMembers->count() > 0)
                        <div class="mb-3">
                            <p class="text-sm text-gray-500 italic">No team members have email addresses configured.</p>
                        </div>
                        @endif

                        <!-- Manual Email Input -->
                        <div class="flex gap-2 mb-3">
                            <input type="email" id="emailInput" placeholder="Enter email address"
                                   onkeypress="if(event.key==='Enter'){event.preventDefault();addEmailFromInput();}"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <button type="button" onclick="addEmailFromInput()"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>

                        <!-- Selected Recipients -->
                        <div id="selectedRecipients" class="space-y-2">
                            <!-- Recipients will be added here dynamically -->
                        </div>
                    </div>

                    <!-- Date Range Section -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Report Date Range <span class="text-red-500">*</span>
                        </label>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">Start Date</label>
                                <input type="date" id="emailStartDate" required
                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">End Date</label>
                                <input type="date" id="emailEndDate" required
                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>

                    <!-- List Type Filter -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Filter by List Type (Optional)
                        </label>
                        <select id="emailListFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">All List Types</option>
                            <option value="features">Features Only</option>
                            <option value="bugs">Bugs Only</option>
                            <option value="other">Other Only</option>
                        </select>
                    </div>

                    <!-- Custom Message -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Custom Message (Optional)
                        </label>
                        <textarea id="customMessage" rows="3" placeholder="Add a personal message to include in the email..."
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                    </div>
                </form>
            </div>

            <!-- Modal Footer -->
            <div class="flex flex-col sm:flex-row items-stretch sm:items-center justify-end pt-4 border-t border-gray-200 space-y-2 sm:space-y-0 sm:space-x-3">
                <button onclick="closeEmailModal()"
                        class="px-4 py-2 bg-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-400 transition-colors order-2 sm:order-1">
                    Cancel
                </button>
                <button onclick="sendEmailReport()" id="sendEmailBtn"
                        class="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors order-1 sm:order-2">
                    <i class="fas fa-envelope-open mr-2"></i>
                    <span class="hidden sm:inline">Open in Email Client</span>
                    <span class="sm:hidden">Send Report</span>
                </button>
            </div>
        </div>
    </div>
</div>
@endsection
