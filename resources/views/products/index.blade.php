@extends('layouts.app')

@section('page-title', 'Products')

@push('styles')
<style>
    .product-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: white;
        border: 1px solid #e5e7eb;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .product-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        border-color: #d1d5db;
    }

    .product-icon {
        transition: all 0.3s ease;
    }

    .product-card:hover .product-icon {
        transform: scale(1.05);
    }

    .phase-badge {
        position: relative;
        overflow: hidden;
    }

    .phase-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }

    .product-card:hover .phase-badge::before {
        left: 100%;
    }

    .floating-action-btn {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        z-index: 50;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
    }

    .floating-action-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    }

    .search-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .stats-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .grid-view {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
        gap: 1.5rem;
    }

    .list-view {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    @media (max-width: 640px) {
        .grid-view {
            grid-template-columns: 1fr;
            gap: 1rem;
        }
    }

    @media (max-width: 768px) {
        .grid-view {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }
    }

    /* Professional gradient backgrounds for status indicators */
    .status-gradient-idea {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    }

    .status-gradient-progress {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }

    .status-gradient-done {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    /* Micro-interactions for better UX */
    .action-button {
        transition: all 0.2s ease;
    }

    .action-button:hover {
        transform: scale(1.05);
    }

    .action-button:active {
        transform: scale(0.95);
    }
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
    <!-- Enhanced Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-xl flex items-center justify-center text-white shadow-lg">
                        <i class="fas fa-cube text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Product Portfolio</h1>
                        <p class="text-sm text-gray-600 mt-1">Manage and track your innovative products across all phases</p>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                    <a href="{{ route('products.create') }}"
                       class="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 text-center">
                        <i class="fas fa-plus mr-2"></i>
                        <span class="hidden sm:inline">Create New Product</span>
                        <span class="sm:hidden">Create</span>
                    </a>
                    <button onclick="toggleView()"
                            class="px-6 py-3 bg-white border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 shadow-sm hover:shadow-md">
                        <i id="view-icon" class="fas fa-th-large mr-2"></i>
                        <span id="view-text">Grid View</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- Enhanced Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200 group">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 group-hover:text-gray-700 transition-colors">Total Products</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">{{ $products->total() }}</p>
                        <p class="text-xs text-blue-600 mt-1">
                            <i class="fas fa-chart-line mr-1"></i>
                            Portfolio overview
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white shadow-sm group-hover:shadow-md transition-all duration-200">
                        <i class="fas fa-cube text-lg"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200 group">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 group-hover:text-gray-700 transition-colors">Ideas</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">{{ $products->where('phase', 'idea')->count() }}</p>
                        <p class="text-xs text-yellow-600 mt-1">
                            <i class="fas fa-lightbulb mr-1"></i>
                            Innovation pipeline
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center text-white shadow-sm group-hover:shadow-md transition-all duration-200">
                        <i class="fas fa-lightbulb text-lg"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200 group">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 group-hover:text-gray-700 transition-colors">In Progress</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">{{ $products->where('phase', 'in_progress')->count() }}</p>
                        <p class="text-xs text-orange-600 mt-1">
                            <i class="fas fa-cogs mr-1"></i>
                            Active development
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center text-white shadow-sm group-hover:shadow-md transition-all duration-200">
                        <i class="fas fa-cogs text-lg"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200 group">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 group-hover:text-gray-700 transition-colors">Completed</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">{{ $products->where('phase', 'done')->count() }}</p>
                        <p class="text-xs text-green-600 mt-1">
                            <i class="fas fa-check-circle mr-1"></i>
                            Successfully delivered
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center text-white shadow-sm group-hover:shadow-md transition-all duration-200">
                        <i class="fas fa-check-circle text-lg"></i>
                    </div>
                </div>
            </div>
        </div>

    <!-- Enhanced Filters -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-filter mr-2 text-purple-600"></i>
                Filter & Search
            </h3>
            <button type="button" id="clear-filters" class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                <i class="fas fa-times mr-1"></i>
                Clear All
            </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="relative">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Products</label>
                <div class="relative">
                    <input type="text" id="search" name="search" placeholder="Search by name or description..."
                           class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                           value="{{ request('search') }}">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </div>

            <div>
                <label for="phase" class="block text-sm font-medium text-gray-700 mb-2">Project Phase</label>
                <div class="relative">
                    <select id="phase" name="phase"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent appearance-none bg-white transition-all duration-300">
                        <option value="">All Phases</option>
                        <option value="idea" {{ request('phase') === 'idea' ? 'selected' : '' }}>💡 Idea</option>
                        <option value="in_progress" {{ request('phase') === 'in_progress' ? 'selected' : '' }}>⚙️ In Progress</option>
                        <option value="done" {{ request('phase') === 'done' ? 'selected' : '' }}>✅ Completed</option>
                    </select>
                    <i class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                </div>
            </div>

            <div class="flex items-end">
                <button type="button" id="filter-btn"
                        class="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-search mr-2"></i>
                    Apply Filters
                </button>
            </div>
        </div>
    </div>

    <!-- Products Display -->
    <div id="products-container" class="grid-view">
        @forelse($products as $product)
            <div class="product-card rounded-xl p-6 cursor-pointer" onclick="window.location.href='{{ route('products.show', $product) }}'">
                <!-- Product Header -->
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="product-icon w-12 h-12 rounded-lg bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center text-white text-xl font-bold">
                            @if($product->hasCustomIcon())
                                <img src="{{ $product->icon_url }}" alt="{{ $product->name }}" class="w-full h-full object-cover rounded-lg">
                            @else
                                {{ substr($product->name, 0, 1) }}
                            @endif
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ $product->name }}</h3>
                            <p class="text-sm text-gray-600">{{ Str::limit($product->description, 60) }}</p>
                        </div>
                    </div>

                    <!-- Phase Badge -->
                    <span class="phase-badge px-3 py-1 text-xs font-medium rounded-full {{ $product->phase_badge_color }}">
                        @switch($product->phase)
                            @case('idea')
                                💡 Idea
                                @break
                            @case('in_progress')
                                ⚙️ In Progress
                                @break
                            @case('done')
                                ✅ Completed
                                @break
                        @endswitch
                    </span>
                </div>

                <!-- Product Details -->
                <div class="space-y-3 mb-4">
                    <!-- Team Members -->
                    @if($product->teamMembers->count() > 0)
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-users text-gray-400 text-sm"></i>
                            <div class="flex -space-x-2">
                                @foreach($product->teamMembers->take(3) as $member)
                                    <div class="w-6 h-6 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white text-xs font-medium border-2 border-white"
                                         title="{{ $member->name }}">
                                        {{ substr($member->name, 0, 1) }}
                                    </div>
                                @endforeach
                                @if($product->teamMembers->count() > 3)
                                    <div class="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 text-xs font-medium border-2 border-white">
                                        +{{ $product->teamMembers->count() - 3 }}
                                    </div>
                                @endif
                            </div>
                            <span class="text-sm text-gray-600">{{ $product->teamMembers->count() }} member(s)</span>
                        </div>
                    @endif

                    <!-- Dates -->
                    <div class="flex items-center justify-between text-sm text-gray-600">
                        @if($product->start_date)
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-calendar-alt text-green-500"></i>
                                <span>Started: {{ $product->start_date->format('M d, Y') }}</span>
                            </div>
                        @endif
                        @if($product->target_date)
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-flag text-orange-500"></i>
                                <span>Target: {{ $product->target_date->format('M d, Y') }}</span>
                            </div>
                        @endif
                    </div>

                    <!-- ClickUp Integration -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            @if($product->clickup_task_id)
                                <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    ClickUp Synced
                                </span>
                            @else
                                <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">
                                    <i class="fas fa-times-circle mr-1"></i>
                                    Not Synced
                                </span>
                            @endif
                        </div>

                        <!-- Documents Count -->
                        @if($product->currentDocuments->count() > 0)
                            <div class="flex items-center space-x-1 text-blue-600">
                                <i class="fas fa-file-alt text-sm"></i>
                                <span class="text-sm">{{ $product->currentDocuments->count() }} doc(s)</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                    <div class="flex space-x-2">
                        <button onclick="event.stopPropagation(); window.location.href='{{ route('products.edit', $product) }}'"
                                class="text-yellow-600 hover:text-yellow-700 p-2 rounded-lg hover:bg-yellow-50 transition-colors">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="event.stopPropagation(); window.location.href='{{ route('products.documents.index', $product) }}'"
                                class="text-blue-600 hover:text-blue-700 p-2 rounded-lg hover:bg-blue-50 transition-colors">
                            <i class="fas fa-folder-open"></i>
                        </button>
                        @if($product->clickup_task_id && isset($product->clickup_data['url']))
                            <a href="{{ $product->clickup_data['url'] }}" target="_blank"
                               onclick="event.stopPropagation()"
                               class="text-purple-600 hover:text-purple-700 p-2 rounded-lg hover:bg-purple-50 transition-colors">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        @endif
                    </div>

                    <button onclick="event.stopPropagation(); window.location.href='{{ route('products.show', $product) }}'"
                            class="text-gray-600 hover:text-gray-700 text-sm font-medium">
                        View Details →
                    </button>
                </div>
            </div>
        @empty
            <div class="col-span-full">
                <div class="text-center py-12">
                    <div class="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-cube text-3xl text-purple-600"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Products Found</h3>
                    <p class="text-gray-600 mb-6">Get started by creating your first product</p>
                    <a href="{{ route('products.create') }}"
                       class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-plus mr-2"></i>
                        Create Your First Product
                    </a>
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($products->hasPages())
        <div class="mt-8">
            {{ $products->links() }}
        </div>
    @endif

    <!-- Floating Action Button -->
    <a href="{{ route('products.create') }}"
       class="floating-action-btn w-16 h-16 rounded-full flex items-center justify-center text-white text-xl">
        <i class="fas fa-plus"></i>
    </a>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Filter functionality
    $('#filter-btn').click(function() {
        loadProducts();
    });

    // Clear filters
    $('#clear-filters').click(function() {
        $('#search').val('');
        $('#phase').val('');
        loadProducts();
    });

    // Search on enter
    $('#search').keypress(function(e) {
        if (e.which == 13) {
            loadProducts();
        }
    });

    // Real-time search
    $('#search').on('input', debounce(function() {
        loadProducts();
    }, 500));

    // Phase filter change
    $('#phase').change(function() {
        loadProducts();
    });

    // Load products with AJAX
    function loadProducts(page = 1) {
        const search = $('#search').val();
        const phase = $('#phase').val();

        // Show loading state
        $('#products-container').html(`
            <div class="col-span-full flex items-center justify-center py-12">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                    <p class="text-gray-600">Loading products...</p>
                </div>
            </div>
        `);

        $.ajax({
            url: '{{ route("products.index") }}',
            type: 'GET',
            data: {
                search: search,
                phase: phase,
                page: page,
                ajax: true
            },
            success: function(response) {
                if (response.html) {
                    $('#products-container').html(response.html);
                } else {
                    // If not AJAX response, reload the page
                    window.location.reload();
                }

                // Update URL without page reload
                const url = new URL(window.location);
                if (search) url.searchParams.set('search', search);
                else url.searchParams.delete('search');
                if (phase) url.searchParams.set('phase', phase);
                else url.searchParams.delete('phase');
                window.history.pushState({}, '', url);
            },
            error: function() {
                $('#products-container').html(`
                    <div class="col-span-full text-center py-12">
                        <div class="text-red-600 mb-4">
                            <i class="fas fa-exclamation-triangle text-4xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Products</h3>
                        <p class="text-gray-600 mb-4">Please try again or refresh the page</p>
                        <button onclick="loadProducts()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                            Try Again
                        </button>
                    </div>
                `);
            }
        });
    }

    // Debounce function for search
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Pagination click handler
    $(document).on('click', '.pagination a', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        const page = new URL(url).searchParams.get('page');
        loadProducts(page);
    });

    // Delete product
    $(document).on('click', '.delete-product', function(e) {
        e.preventDefault();
        
        if (!confirm('Are you sure you want to delete this product?')) {
            return;
        }

        const url = $(this).attr('href');
        
        $.ajax({
            url: url,
            type: 'DELETE',
            success: function(response) {
                if (response.success) {
                    loadProducts();
                    alert(response.message);
                }
            },
            error: function() {
                alert('Error deleting product');
            }
        });
    });
});

// View toggle functionality
function toggleView() {
    const container = document.getElementById('products-container');
    const icon = document.getElementById('view-icon');
    const text = document.getElementById('view-text');

    if (container.classList.contains('grid-view')) {
        // Switch to list view
        container.classList.remove('grid-view');
        container.classList.add('list-view');
        icon.className = 'fas fa-th mr-2';
        text.textContent = 'Card View';

        // Update card styles for list view
        const cards = container.querySelectorAll('.product-card');
        cards.forEach(card => {
            card.classList.add('flex', 'items-center', 'space-x-6');
            card.style.minHeight = '120px';
        });
    } else {
        // Switch to grid view
        container.classList.remove('list-view');
        container.classList.add('grid-view');
        icon.className = 'fas fa-th-large mr-2';
        text.textContent = 'List View';

        // Update card styles for grid view
        const cards = container.querySelectorAll('.product-card');
        cards.forEach(card => {
            card.classList.remove('flex', 'items-center', 'space-x-6');
            card.style.minHeight = 'auto';
        });
    }
}
</script>
@endpush
@endsection
