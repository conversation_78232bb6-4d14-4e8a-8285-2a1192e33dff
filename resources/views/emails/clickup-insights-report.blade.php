<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClickUp Progress Report - {{ $product->name }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1f2937;
            margin: 0;
            font-size: 28px;
        }
        .header .subtitle {
            color: #6b7280;
            margin: 5px 0 0 0;
            font-size: 16px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 20px;
        }
        .product-overview {
            background: #f3f4f6;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .product-overview .row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .product-overview .label {
            font-weight: 600;
            color: #374151;
        }
        .product-overview .value {
            color: #6b7280;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
        }
        .stat-card .number {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }
        .stat-card .label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .list-item {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 15px;
            background: #fafafa;
        }
        .list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .list-name {
            font-weight: 600;
            color: #1f2937;
            font-size: 16px;
        }
        .list-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }
        .list-type.features { background: #dbeafe; color: #1e40af; }
        .list-type.bugs { background: #fee2e2; color: #dc2626; }
        .list-type.other { background: #f3f4f6; color: #374151; }
        .hierarchical-path {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #6b7280;
            background: #f9fafb;
            padding: 6px 10px;
            border-radius: 4px;
            border: 1px solid #e5e7eb;
            margin-bottom: 15px;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        .progress-fill {
            height: 100%;
            background: #10b981;
            transition: width 0.3s ease;
        }
        .task-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            text-align: center;
        }
        .task-stat {
            padding: 8px;
            border-radius: 4px;
            background: white;
        }
        .task-stat .number {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 2px;
        }
        .task-stat .label {
            font-size: 11px;
            color: #6b7280;
            text-transform: uppercase;
        }
        .completion-rate {
            font-weight: bold;
            color: #059669;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }
        .custom-message {
            background: #eff6ff;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 6px 6px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>📊 ClickUp Progress Report</h1>
            <p class="subtitle">{{ $product->name }} • {{ \Carbon\Carbon::parse($startDate)->format('M d, Y') }} - {{ \Carbon\Carbon::parse($endDate)->format('M d, Y') }}</p>
        </div>

        <!-- Custom Message -->
        @if($customMessage)
        <div class="custom-message">
            <strong>Message:</strong> {{ $customMessage }}
        </div>
        @endif

        <!-- Product Overview -->
        <div class="section">
            <h2>📋 Product Overview</h2>
            <div class="product-overview">
                <div class="row">
                    <span class="label">Product Name:</span>
                    <span class="value">{{ $product->name }}</span>
                </div>
                <div class="row">
                    <span class="label">Current Phase:</span>
                    <span class="value">{{ ucfirst($product->phase) }}</span>
                </div>
                @if($product->start_date)
                <div class="row">
                    <span class="label">Start Date:</span>
                    <span class="value">{{ $product->start_date->format('M d, Y') }}</span>
                </div>
                @endif
                @if($product->target_date)
                <div class="row">
                    <span class="label">Target Date:</span>
                    <span class="value">{{ $product->target_date->format('M d, Y') }}</span>
                </div>
                @endif
                @if($product->description)
                <div class="row">
                    <span class="label">Description:</span>
                    <span class="value">{{ $product->description }}</span>
                </div>
                @endif
            </div>
        </div>

        <!-- Overall Statistics -->
        <div class="section">
            <h2>📈 Overall Statistics</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="number">{{ $overallStats['total_tasks'] }}</div>
                    <div class="label">Total Tasks</div>
                </div>
                <div class="stat-card">
                    <div class="number">{{ $overallStats['total_completed'] }}</div>
                    <div class="label">Completed</div>
                </div>
                <div class="stat-card">
                    <div class="number">{{ $overallStats['total_in_progress'] }}</div>
                    <div class="label">In Progress</div>
                </div>
                <div class="stat-card">
                    <div class="number">{{ $overallStats['completion_rate'] }}%</div>
                    <div class="label">Completion Rate</div>
                </div>
                @if($overallStats['avg_completion_time'] > 0)
                <div class="stat-card">
                    <div class="number">{{ $overallStats['avg_completion_time'] }}</div>
                    <div class="label">Avg Days to Complete</div>
                </div>
                @endif
                <div class="stat-card">
                    <div class="number">{{ $overallStats['total_lists'] }}</div>
                    <div class="label">Connected Lists</div>
                </div>
            </div>
        </div>

        <!-- Progress by List -->
        <div class="section">
            <h2>📝 Progress by ClickUp List</h2>
            @forelse($progressData as $list)
                @php
                    $progress = $list['progress'];
                    $completionRate = $progress['total_tasks'] > 0 ? round(($progress['completed_tasks'] / $progress['total_tasks']) * 100, 1) : 0;
                @endphp
                <div class="list-item">
                    <div class="list-header">
                        <div class="list-name">{{ $list['list_name'] }}</div>
                        <div class="list-type {{ $list['list_type'] }}">{{ $list['list_type_name'] }}</div>
                    </div>
                    
                    <div class="hierarchical-path">
                        🗂️ {{ $list['hierarchical_path'] }}
                    </div>
                    
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {{ $completionRate }}%"></div>
                    </div>
                    
                    <div style="text-align: center; margin-bottom: 15px;">
                        <span class="completion-rate">{{ $completionRate }}% Complete</span>
                    </div>
                    
                    <div class="task-stats">
                        <div class="task-stat">
                            <div class="number">{{ $progress['total_tasks'] }}</div>
                            <div class="label">Total</div>
                        </div>
                        <div class="task-stat">
                            <div class="number" style="color: #059669;">{{ $progress['completed_tasks'] }}</div>
                            <div class="label">Completed</div>
                        </div>
                        <div class="task-stat">
                            <div class="number" style="color: #d97706;">{{ $progress['in_progress_tasks'] }}</div>
                            <div class="label">In Progress</div>
                        </div>
                        <div class="task-stat">
                            <div class="number" style="color: #6b7280;">{{ $progress['open_tasks'] }}</div>
                            <div class="label">Open</div>
                        </div>
                    </div>
                    
                    @if($progress['avg_completion_time_days'] > 0)
                    <div style="margin-top: 10px; text-align: center; font-size: 14px; color: #6b7280;">
                        ⏱️ Average completion time: <strong>{{ $progress['avg_completion_time_days'] }} days</strong>
                    </div>
                    @endif
                </div>
            @empty
                <p style="text-align: center; color: #6b7280; font-style: italic;">No ClickUp lists are currently assigned to this product.</p>
            @endforelse
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>This report was generated on {{ now()->format('M d, Y \a\t g:i A') }}</p>
            <p>Generated by the Product Management System</p>
        </div>
    </div>
</body>
</html>
