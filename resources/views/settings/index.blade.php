@extends('layouts.app')

@section('page-title', 'Settings')

@section('content')
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div>
        <h1 class="text-2xl font-bold text-gray-900">Settings</h1>
        <p class="text-gray-600">Configure application settings and integrations</p>
    </div>

    <form action="{{ route('settings.update') }}" method="POST" class="space-y-6">
        @csrf
        @method('PUT')

        <!-- ClickUp Integration -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">ClickUp Integration</h2>
                    <p class="text-sm text-gray-600">Connect with ClickUp to sync products and tasks</p>
                </div>
                @if($clickupStatus)
                    <div class="flex items-center space-x-2">
                        @if($clickupStatus['success'])
                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                <i class="fas fa-check-circle mr-1"></i>
                                Connected
                            </span>
                        @else
                            <span class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                Error
                            </span>
                        @endif
                    </div>
                @endif
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="clickup_api_token" class="block text-sm font-medium text-gray-700 mb-2">
                        API Token
                        <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <input type="password" id="clickup_api_token" name="clickup_api_token"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                               value="{{ $settings['clickup_api_token'] }}"
                               placeholder="pk_...">
                        <button type="button" id="toggle-token" class="absolute right-2 top-2 text-gray-400 hover:text-gray-600">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">
                        Get your API token from ClickUp Settings → Apps
                    </p>
                    @error('clickup_api_token')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="clickup_team_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Team ID
                    </label>
                    <div class="flex space-x-2">
                        <select id="clickup_team_id" name="clickup_team_id"
                                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            <option value="">Select Team</option>
                        </select>
                        <button type="button" id="load-teams" class="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                            <i class="fas fa-sync"></i>
                        </button>
                    </div>
                    @error('clickup_team_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="clickup_space_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Space/List ID
                    </label>
                    <div class="flex space-x-2">
                        <select id="clickup_space_id" name="clickup_space_id"
                                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            <option value="">Select Space</option>
                        </select>
                        <button type="button" id="load-spaces" class="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700" disabled>
                            <i class="fas fa-sync"></i>
                        </button>
                    </div>
                    @error('clickup_space_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="clickup_sync_enabled" name="clickup_sync_enabled" value="1"
                           {{ $settings['clickup_sync_enabled'] ? 'checked' : '' }}
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                    <label for="clickup_sync_enabled" class="ml-2 block text-sm text-gray-900">
                        Enable automatic synchronization
                    </label>
                </div>
            </div>

            <div class="mt-4 flex space-x-2">
                <button type="button" id="test-connection" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i class="fas fa-plug mr-2"></i>
                    Test Connection
                </button>
                @if($clickupStatus && !$clickupStatus['success'])
                    <div class="text-sm text-red-600 flex items-center">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        {{ $clickupStatus['message'] }}
                    </div>
                @endif
            </div>
        </div>

        <!-- AI Integration -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">AI Integration</h2>
                    <p class="text-sm text-gray-600">Configure Google Gemini AI for enhanced features</p>
                </div>
                <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                    <i class="fas fa-star mr-1"></i>
                    Premium
                </span>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="gemini_api_key" class="block text-sm font-medium text-gray-700 mb-2">
                        Gemini API Key
                    </label>
                    <input type="password" id="gemini_api_key" name="gemini_api_key"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ $settings['gemini_api_key'] }}"
                           placeholder="AIza...">
                    <p class="text-xs text-gray-500 mt-1">
                        Get your API key from Google AI Studio
                    </p>
                    @error('gemini_api_key')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="gemini_ai_enabled" name="gemini_ai_enabled" value="1"
                           {{ $settings['gemini_ai_enabled'] ? 'checked' : '' }}
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                    <label for="gemini_ai_enabled" class="ml-2 block text-sm text-gray-900">
                        Enable AI features
                    </label>
                </div>
            </div>
        </div>

        <!-- General Settings -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">General Settings</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="app_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Application Name
                        <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="app_name" name="app_name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ $settings['app_name'] }}">
                    @error('app_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="reports_retention_days" class="block text-sm font-medium text-gray-700 mb-2">
                        Reports Retention (Days)
                        <span class="text-red-500">*</span>
                    </label>
                    <input type="number" id="reports_retention_days" name="reports_retention_days" required min="1" max="3650"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           value="{{ $settings['reports_retention_days'] }}">
                    <p class="text-xs text-gray-500 mt-1">
                        How long to keep report data (1-3650 days)
                    </p>
                    @error('reports_retention_days')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="flex justify-end">
            <button type="submit" class="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 font-medium">
                <i class="fas fa-save mr-2"></i>
                Save Settings
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Toggle API token visibility
    $('#toggle-token').click(function() {
        const input = $('#clickup_api_token');
        const icon = $(this).find('i');
        
        if (input.attr('type') === 'password') {
            input.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            input.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // Test ClickUp connection
    $('#test-connection').click(function() {
        const button = $(this);
        const apiToken = $('#clickup_api_token').val();
        
        if (!apiToken) {
            alert('Please enter an API token first');
            return;
        }

        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Testing...');

        $.ajax({
            url: '{{ route("settings.test-clickup") }}',
            type: 'POST',
            data: {
                api_token: apiToken
            },
            success: function(response) {
                if (response.success) {
                    alert('Connection successful!');
                } else {
                    alert('Connection failed: ' + response.message);
                }
            },
            error: function() {
                alert('Error testing connection');
            },
            complete: function() {
                button.prop('disabled', false).html('<i class="fas fa-plug mr-2"></i>Test Connection');
            }
        });
    });

    // Load ClickUp teams
    $('#load-teams').click(function() {
        const button = $(this);
        const select = $('#clickup_team_id');
        const apiToken = $('#clickup_api_token').val();

        if (!apiToken) {
            alert('Please enter an API token first');
            return;
        }

        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');

        $.ajax({
            url: '{{ route("settings.clickup-teams") }}',
            type: 'GET',
            data: {
                api_token: apiToken
            },
            success: function(response) {
                if (response.success) {
                    select.empty().append('<option value="">Select Team</option>');
                    response.teams.forEach(function(team) {
                        select.append(`<option value="${team.id}">${team.name}</option>`);
                    });

                    // Set current value if exists
                    const currentTeamId = '{{ $settings["clickup_team_id"] }}';
                    if (currentTeamId) {
                        select.val(currentTeamId);
                    }

                    $('#load-spaces').prop('disabled', false);
                } else {
                    alert('Failed to load teams: ' + response.message);
                }
            },
            error: function() {
                alert('Error loading teams');
            },
            complete: function() {
                button.prop('disabled', false).html('<i class="fas fa-sync"></i>');
            }
        });
    });

    // Load ClickUp spaces when team changes
    $('#clickup_team_id').change(function() {
        const teamId = $(this).val();
        if (teamId) {
            loadSpaces(teamId);
        } else {
            $('#clickup_space_id').empty().append('<option value="">Select Space</option>');
            $('#load-spaces').prop('disabled', true);
        }
    });

    // Load spaces button
    $('#load-spaces').click(function() {
        const teamId = $('#clickup_team_id').val();
        if (teamId) {
            loadSpaces(teamId);
        }
    });

    function loadSpaces(teamId) {
        const button = $('#load-spaces');
        const select = $('#clickup_space_id');
        const apiToken = $('#clickup_api_token').val();

        if (!apiToken) {
            alert('Please enter an API token first');
            return;
        }

        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');

        $.ajax({
            url: '{{ route("settings.clickup-spaces") }}',
            type: 'GET',
            data: {
                team_id: teamId,
                api_token: apiToken
            },
            success: function(response) {
                if (response.success) {
                    select.empty().append('<option value="">Select Space</option>');
                    response.spaces.forEach(function(space) {
                        select.append(`<option value="${space.id}">${space.name}</option>`);
                    });

                    // Set current value if exists
                    const currentSpaceId = '{{ $settings["clickup_space_id"] }}';
                    if (currentSpaceId) {
                        select.val(currentSpaceId);
                    }
                } else {
                    alert('Failed to load spaces: ' + response.message);
                }
            },
            error: function() {
                alert('Error loading spaces');
            },
            complete: function() {
                button.prop('disabled', false).html('<i class="fas fa-sync"></i>');
            }
        });
    }

    // Auto-load teams and spaces if API token exists
    @if(!empty($settings['clickup_api_token']))
        $('#load-teams').click();
    @endif
});
</script>
@endpush
@endsection
