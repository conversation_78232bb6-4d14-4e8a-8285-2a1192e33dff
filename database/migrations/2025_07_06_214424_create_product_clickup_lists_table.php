<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_clickup_lists', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->string('clickup_list_id');
            $table->json('clickup_list_data')->nullable(); // Store list metadata
            $table->enum('list_type', ['features', 'bugs', 'other'])->default('other');
            $table->timestamps();

            // Ensure unique combination of product and list
            $table->unique(['product_id', 'clickup_list_id']);

            // Index for better performance
            $table->index(['product_id', 'list_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_clickup_lists');
    }
};
