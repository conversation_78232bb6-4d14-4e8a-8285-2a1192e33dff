<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('file_name');
            $table->string('file_path');
            $table->string('file_type');
            $table->integer('file_size'); // in bytes
            $table->string('version', 20)->default('1.0');
            $table->integer('version_major')->default(1);
            $table->integer('version_minor')->default(0);
            $table->integer('version_patch')->default(0);
            $table->foreignId('uploaded_by')->nullable()->constrained('team_members')->onDelete('set null');
            $table->boolean('is_current_version')->default(true);
            $table->timestamp('uploaded_at');
            $table->timestamps();

            // Indexes for better performance
            $table->index(['product_id', 'is_current_version']);
            $table->index(['product_id', 'version']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_documents');
    }
};
