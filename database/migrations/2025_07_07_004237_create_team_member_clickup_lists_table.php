<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('team_member_clickup_lists', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_member_id')->constrained()->onDelete('cascade');
            $table->string('clickup_list_id');
            $table->enum('list_type', ['features', 'bugs', 'other'])->default('other');
            $table->json('clickup_list_data')->nullable(); // Store list details (name, space, folder, etc.)
            $table->json('clickup_space_data')->nullable(); // Store space details
            $table->json('clickup_folder_data')->nullable(); // Store folder details (if applicable)
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable(); // Optional notes about the assignment
            $table->timestamps();

            // Indexes for better performance
            $table->index(['team_member_id', 'list_type']);
            $table->index(['clickup_list_id']);
            $table->index(['is_active']);

            // Unique constraint to prevent duplicate assignments
            $table->unique(['team_member_id', 'clickup_list_id'], 'unique_team_member_clickup_list');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('team_member_clickup_lists');
    }
};
